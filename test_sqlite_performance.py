#!/usr/bin/env python3
"""
Test script để đánh giá hiệu suất SQLite Buffer Writer
So s<PERSON>h với Direct Excel Writer hiện tại
"""

import time
import random
import os
import sys
from data_scrape_core import SQLiteBufferExcelWriter, DirectExcelWriter

def generate_test_products(num_products=1000):
    """Tạo dữ liệu test sản phẩm"""
    products = []
    for i in range(num_products):
        product = {
            'id': f'product_{i:06d}',
            'shopID': f'shop_{random.randint(1, 100):03d}',
            'linkProduct': f'https://shopee.vn/product-{i}',
            'linkShop': f'https://shopee.vn/shop-{random.randint(1, 100)}',
            'name': f'Sản phẩm test số {i} - {random.choice(["Áo", "Quần", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> hồ"])}',
            'brand': random.choice(['Nike', 'Adidas', 'Samsung', 'Apple', 'Xiaomi']),
            'description': f'<PERSON><PERSON> tả chi tiết cho sản phẩm {i}. ' * random.randint(1, 5),
            'timeCreate': f'2024-01-{random.randint(1, 28):02d}',
            'itemID': f'item_{i}',
            'categoryMain': random.choice(['Thời trang', 'Điện tử', 'Gia dụng', 'Thể thao']),
            'categoryTree': f'Category > Subcategory > Item {i}',
            'price': str(random.randint(10000, 1000000)),
            'priceMin': str(random.randint(10000, 500000)),
            'priceMax': str(random.randint(500000, 1000000)),
            'discount': str(random.randint(0, 50)),
            'stock': str(random.randint(0, 1000)),
            'weight': str(random.randint(100, 5000)),
            'image': f'https://cf.shopee.vn/file/image_{i}.jpg',
            'cmtCount': str(random.randint(0, 1000)),
            'viewCount': str(random.randint(100, 10000)),
            'likedCount': str(random.randint(0, 500)),
            'itemRating': str(round(random.uniform(3.0, 5.0), 1)),
            'sold_30day': str(random.randint(0, 100)),
            'sale_30day': str(random.randint(0, 10000000))
        }
        products.append(product)
    return products

def test_writer_performance(writer_class, writer_name, test_products, output_file):
    """Test hiệu suất của một writer class"""
    print(f"\n🧪 Testing {writer_name}...")
    print(f"📊 Số sản phẩm test: {len(test_products):,}")
    
    start_time = time.time()
    
    try:
        # Khởi tạo writer
        writer = writer_class(output_file, log_callback=print)
        
        # Simulate 8 threads writing - chia products thành 8 batches
        batch_size = len(test_products) // 8
        batches = [test_products[i:i + batch_size] for i in range(0, len(test_products), batch_size)]
        
        write_start = time.time()
        
        # Ghi từng batch (simulate concurrent writes)
        for i, batch in enumerate(batches):
            if batch:  # Đảm bảo batch không rỗng
                writer.append_products(batch)
                print(f"📦 Batch {i+1}/8: {len(batch)} sản phẩm")
        
        write_end = time.time()
        write_time = write_end - write_start
        
        # Finalize
        finalize_start = time.time()
        success = writer.finalize()
        finalize_end = time.time()
        finalize_time = finalize_end - finalize_start
        
        total_time = time.time() - start_time
        
        if success:
            file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
            print(f"✅ {writer_name} THÀNH CÔNG!")
            print(f"⏱️  Thời gian ghi: {write_time:.2f}s")
            print(f"⏱️  Thời gian finalize: {finalize_time:.2f}s")
            print(f"⏱️  Tổng thời gian: {total_time:.2f}s")
            print(f"📁 Kích thước file: {file_size:.2f} MB")
            print(f"🚀 Tốc độ: {len(test_products)/total_time:.0f} sản phẩm/giây")
            
            return {
                'success': True,
                'write_time': write_time,
                'finalize_time': finalize_time,
                'total_time': total_time,
                'file_size': file_size,
                'speed': len(test_products)/total_time
            }
        else:
            print(f"❌ {writer_name} THẤT BẠI!")
            return {'success': False}
            
    except Exception as e:
        print(f"❌ Lỗi {writer_name}: {str(e)}")
        return {'success': False, 'error': str(e)}

def main():
    """Chạy performance test"""
    print("🔬 SQLite Buffer Writer Performance Test")
    print("=" * 50)
    
    # Tạo test data với các kích thước khác nhau
    test_sizes = [1000, 5000, 10000]  # Bắt đầu với số lượng nhỏ
    
    for num_products in test_sizes:
        print(f"\n🎯 TEST với {num_products:,} sản phẩm")
        print("-" * 40)
        
        # Tạo test data
        test_products = generate_test_products(num_products)
        
        # Test SQLite Buffer Writer
        sqlite_file = f"test_sqlite_{num_products}.xlsx"
        sqlite_results = test_writer_performance(
            SQLiteBufferExcelWriter, 
            "SQLite Buffer Writer", 
            test_products, 
            sqlite_file
        )
        
        # Test Direct Excel Writer (để so sánh)
        direct_file = f"test_direct_{num_products}.xlsx"
        direct_results = test_writer_performance(
            DirectExcelWriter, 
            "Direct Excel Writer", 
            test_products, 
            direct_file
        )
        
        # So sánh kết quả
        if sqlite_results['success'] and direct_results['success']:
            print(f"\n📊 SO SÁNH KẾT QUẢ ({num_products:,} sản phẩm):")
            print(f"SQLite Buffer: {sqlite_results['total_time']:.2f}s ({sqlite_results['speed']:.0f} sp/s)")
            print(f"Direct Excel:  {direct_results['total_time']:.2f}s ({direct_results['speed']:.0f} sp/s)")
            
            if sqlite_results['total_time'] < direct_results['total_time']:
                improvement = (direct_results['total_time'] - sqlite_results['total_time']) / direct_results['total_time'] * 100
                print(f"🚀 SQLite Buffer NHANH HƠN {improvement:.1f}%")
            else:
                slower = (sqlite_results['total_time'] - direct_results['total_time']) / direct_results['total_time'] * 100
                print(f"⚠️  SQLite Buffer CHẬM HƠN {slower:.1f}%")
        
        # Cleanup test files
        for file in [sqlite_file, direct_file]:
            if os.path.exists(file):
                try:
                    os.remove(file)
                    print(f"🗑️  Đã xóa {file}")
                except:
                    pass

if __name__ == "__main__":
    main()
