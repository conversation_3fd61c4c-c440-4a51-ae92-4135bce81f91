#!/usr/bin/env python3
"""
Test SQLite Buffer Writer với production scale
Mô phỏng 1000 shop IDs với 160k-200k products
"""

import time
import random
import os
import threading
from concurrent.futures import ThreadPoolExecutor
from data_scrape_core import SQLiteBufferExcelWriter

def generate_shop_products(shop_id, num_products):
    """Tạo products cho một shop (mô phỏng API response)"""
    products = []
    for i in range(num_products):
        product = {
            'id': f'product_{shop_id}_{i:04d}',
            'shopID': f'shop_{shop_id:04d}',
            'linkProduct': f'https://shopee.vn/product-{shop_id}-{i}',
            'linkShop': f'https://shopee.vn/shop-{shop_id}',
            'name': f'Sản phẩm {i} từ shop {shop_id}',
            'brand': random.choice(['Nike', 'Adidas', 'Samsung', 'Apple', 'Xiaomi']),
            'description': f'<PERSON><PERSON> tả sản phẩm {i} từ shop {shop_id}',
            'timeCreate': f'2024-01-{random.randint(1, 28):02d}',
            'itemID': f'item_{shop_id}_{i}',
            'categoryMain': random.choice(['Thời trang', 'Điện tử', 'Gia dụng']),
            'categoryTree': f'Category > Subcategory > Item {i}',
            'price': str(random.randint(10000, 1000000)),
            'priceMin': str(random.randint(10000, 500000)),
            'priceMax': str(random.randint(500000, 1000000)),
            'discount': str(random.randint(0, 50)),
            'stock': str(random.randint(0, 1000)),
            'weight': str(random.randint(100, 5000)),
            'image': f'https://cf.shopee.vn/file/image_{shop_id}_{i}.jpg',
            'cmtCount': str(random.randint(0, 1000)),
            'viewCount': str(random.randint(100, 10000)),
            'likedCount': str(random.randint(0, 500)),
            'itemRating': str(round(random.uniform(3.0, 5.0), 1)),
            'sold_30day': str(random.randint(0, 100)),
            'sale_30day': str(random.randint(0, 10000000))
        }
        products.append(product)
    return products

def simulate_shop_scraping(shop_id, writer, results_dict, log_callback=None):
    """Mô phỏng việc scrape một shop (chạy trong thread riêng)"""
    def log(msg):
        if log_callback:
            log_callback(f"Shop {shop_id:04d}: {msg}")
    
    try:
        # Mô phỏng thời gian scraping (1-3 giây per shop)
        scrape_time = random.uniform(1.0, 3.0)
        time.sleep(scrape_time)
        
        # Tạo số lượng products ngẫu nhiên (100-300 per shop)
        num_products = random.randint(100, 300)
        products = generate_shop_products(shop_id, num_products)
        
        # Ghi vào SQLite buffer (siêu nhanh)
        write_start = time.time()
        writer.append_products(products)
        write_time = time.time() - write_start
        
        results_dict[shop_id] = {
            'products': num_products,
            'scrape_time': scrape_time,
            'write_time': write_time,
            'success': True
        }
        
        log(f"✅ {num_products} sản phẩm, scrape: {scrape_time:.2f}s, write: {write_time:.4f}s")
        
    except Exception as e:
        results_dict[shop_id] = {
            'products': 0,
            'scrape_time': 0,
            'write_time': 0,
            'success': False,
            'error': str(e)
        }
        log(f"❌ Lỗi: {str(e)}")

def test_production_scale():
    """Test với production scale"""
    print("🏭 PRODUCTION SCALE TEST - SQLite Buffer Writer")
    print("=" * 60)
    
    # Production parameters
    num_shops = 100  # Bắt đầu với 100 shops (có thể tăng lên 1000)
    max_workers = 8  # 8 threads như production
    output_file = "production_test.xlsx"
    
    print(f"🎯 Mô phỏng {num_shops} shops với {max_workers} threads")
    print(f"📊 Ước tính: {num_shops * 200} sản phẩm")
    print("-" * 60)
    
    # Khởi tạo SQLite Buffer Writer
    start_time = time.time()
    writer = SQLiteBufferExcelWriter(output_file, log_callback=print)
    
    # Results tracking
    results = {}
    
    # Simulate concurrent scraping với ThreadPoolExecutor
    print("🚀 Bắt đầu mô phỏng scraping với 8 threads...")
    scraping_start = time.time()
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit tất cả shop tasks
        futures = []
        for shop_id in range(1, num_shops + 1):
            future = executor.submit(
                simulate_shop_scraping, 
                shop_id, 
                writer, 
                results
            )
            futures.append(future)
        
        # Đợi tất cả hoàn thành
        completed = 0
        for future in futures:
            future.result()  # Đợi hoàn thành
            completed += 1
            if completed % 10 == 0:
                print(f"📈 Tiến độ: {completed}/{num_shops} shops hoàn thành")
    
    scraping_end = time.time()
    scraping_time = scraping_end - scraping_start
    
    print(f"\n✅ Scraping hoàn thành trong {scraping_time:.2f}s")
    
    # Finalize và export Excel
    print("📊 Bắt đầu export SQLite → Excel...")
    finalize_start = time.time()
    success = writer.finalize()
    finalize_time = time.time() - finalize_start
    
    total_time = time.time() - start_time
    
    # Phân tích kết quả
    if success:
        successful_shops = sum(1 for r in results.values() if r['success'])
        total_products = sum(r['products'] for r in results.values() if r['success'])
        avg_scrape_time = sum(r['scrape_time'] for r in results.values() if r['success']) / successful_shops
        avg_write_time = sum(r['write_time'] for r in results.values() if r['success']) / successful_shops
        
        file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
        
        print(f"\n🎉 PRODUCTION TEST THÀNH CÔNG!")
        print(f"=" * 60)
        print(f"📊 Shops thành công: {successful_shops}/{num_shops}")
        print(f"📦 Tổng sản phẩm: {total_products:,}")
        print(f"⏱️  Thời gian scraping: {scraping_time:.2f}s")
        print(f"⏱️  Thời gian export: {finalize_time:.2f}s")
        print(f"⏱️  Tổng thời gian: {total_time:.2f}s")
        print(f"📁 Kích thước file: {file_size:.2f} MB")
        print(f"🚀 Tốc độ tổng thể: {total_products/total_time:.0f} sản phẩm/giây")
        print(f"📈 Tốc độ scraping: {total_products/scraping_time:.0f} sản phẩm/giây")
        print(f"📈 Tốc độ export: {total_products/finalize_time:.0f} sản phẩm/giây")
        print(f"⚡ Avg write time per shop: {avg_write_time*1000:.2f}ms")
        
        # Performance analysis
        print(f"\n📊 PERFORMANCE ANALYSIS:")
        print(f"- Scraping chiếm {scraping_time/total_time*100:.1f}% thời gian")
        print(f"- Export chiếm {finalize_time/total_time*100:.1f}% thời gian")
        print(f"- SQLite write overhead: {avg_write_time*1000:.2f}ms per shop (negligible)")
        
        # Extrapolate to full production
        print(f"\n🔮 EXTRAPOLATION TO 1000 SHOPS:")
        shops_ratio = 1000 / num_shops
        est_products = total_products * shops_ratio
        est_scraping_time = scraping_time * shops_ratio
        est_export_time = finalize_time * (shops_ratio ** 0.8)  # Export scales sub-linearly
        est_total_time = est_scraping_time + est_export_time
        
        print(f"📦 Ước tính sản phẩm: {est_products:,.0f}")
        print(f"⏱️  Ước tính thời gian scraping: {est_scraping_time/60:.1f} phút")
        print(f"⏱️  Ước tính thời gian export: {est_export_time/60:.1f} phút")
        print(f"⏱️  Ước tính tổng thời gian: {est_total_time/60:.1f} phút")
        
    else:
        print("❌ PRODUCTION TEST THẤT BẠI!")
    
    # Cleanup
    if os.path.exists(output_file):
        os.remove(output_file)
        print(f"🗑️  Đã xóa {output_file}")

if __name__ == "__main__":
    test_production_scale()
