import time
import requests
import pandas as pd
import os
import csv
import datetime
import concurrent.futures
import threading
import re
import random
import sqlite3
import tempfile
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import <PERSON><PERSON><PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.styles import Font
from openpyxl.cell.cell import ILLEGAL_CHARACTERS_RE

# Pool User-Agent để chống fingerprinting
USER_AGENT_POOL = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36"
]

# Pool Accept-Language để variation
ACCEPT_LANGUAGE_POOL = [
    "vi,en-US;q=0.9,en;q=0.8",
    "vi-VN,vi;q=0.9,en;q=0.8",
    "vi,en;q=0.9",
    "vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7"
]

# ==========================================
# ANTI-BOT STRATEGY 4 - RETRY ENHANCED
# ==========================================
# 1. 8 luồng đồng thời - tối ưu hiệu suất
# 2. Random delays: 2-5s giữa request, 8-15s giữa shop
# 3. User-Agent rotation với tần suất thấp (30% chance)
# 4. Headers variation ổn định với fallback
# 5. Session refresh mỗi 100 shop để reset fingerprint
# 6. Enhanced retry: 3^n backoff (3,9,27s) + jitter
# 7. Proper empty response handling (không dừng ngay)
# 8. STREAMING EXCEL WRITE - ghi ngay khi có data
# 9. SHOP 0-PRODUCT RETRY - retry 2 lần với delay 15-25s
# ==========================================

# Biến lưu trữ thông tin đăng nhập để tái sử dụng
_auth_lock = threading.Lock()
_auth_info = {
    'cookies': None,
    'headers': None,
    'expiry_time': None
}

def get_logged_in_cookies(username, password, headless=True, force_refresh=False):
    """
    Lấy cookies đăng nhập, sử dụng cache để tránh đăng nhập lại nhiều lần

    Args:
        username: Tên đăng nhập
        password: Mật khẩu
        headless: Chạy ở chế độ headless
        force_refresh: Buộc làm mới thông tin đăng nhập bỏ qua cache

    Returns:
        dict: Cookie đăng nhập dạng dict
    """
    with _auth_lock:
        current_time = time.time()
        # Kiểm tra cache có hết hạn không (hết hạn sau 1 giờ)
        if not force_refresh and _auth_info['cookies'] and _auth_info['expiry_time'] and current_time < _auth_info['expiry_time']:
            return _auth_info['cookies']

        options = Options()
        if headless:
            options.add_argument("--headless=new")
        options.add_argument("--start-maximized")
        options.add_argument("--disable-extensions")
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--no-sandbox")
        driver = webdriver.Chrome(options=options)

        try:
            driver.get("https://autoshopee.com/login")

            # Sử dụng WebDriverWait thay vì time.sleep cố định
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "#login-username"))
            )

            driver.find_element(By.CSS_SELECTOR, "#login-username").send_keys(username)
            driver.find_element(By.CSS_SELECTOR, "#login-password").send_keys(password)
            driver.find_element(By.CSS_SELECTOR, "form button[type='submit']").click()

            # Đợi redirect đến trang dashboard
            WebDriverWait(driver, 10).until(
                lambda d: "dashboard" in d.current_url
            )

            if "dashboard" not in driver.current_url:
                driver.quit()
                raise Exception("❌ Login thất bại, kiểm tra lại thông tin đăng nhập")

            cookies = driver.get_cookies()

            # Lấy CSRF token và các thông tin xác thực khác
            csrf_token = driver.execute_script("""
                // Thử lấy từ cookie
                let match = document.cookie.match(/_csrf=([^;]+)/);
                if (match) return match[1];

                // Thử lấy từ meta tag
                let meta = document.querySelector('meta[name="csrf-token"]');
                if (meta) return meta.getAttribute('content');

                // Thử lấy từ form input
                let input = document.querySelector('input[name="_csrf"]');
                if (input) return input.value;

                return null;
            """)

            # Lấy cookies từ Selenium
            cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
            cookie_string = "; ".join([f"{k}={v}" for k, v in cookie_dict.items()])

            # Lấy token từ nhiều nguồn khác nhau
            token_sources = [
                "return window.localStorage.getItem('_csrf') || window.localStorage.getItem('csrf');",
                "return window.sessionStorage.getItem('_csrf') || window.sessionStorage.getItem('csrf');",
                "return document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content');",
                "return document.querySelector('input[name=\"_csrf\"]')?.value;"
            ]

            token = None
            for script in token_sources:
                token = driver.execute_script(script)
                if token:
                    break

            if not token and csrf_token:
                token = csrf_token

            # Tạo headers cho request API với random User-Agent
            selected_ua = random.choice(USER_AGENT_POOL)
            selected_lang = random.choice(ACCEPT_LANGUAGE_POOL)

            headers = {
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": selected_lang,
                "Accept-Encoding": "gzip, deflate, br",
                "Cache-Control": "no-cache",
                "Content-Type": "application/json;charset=UTF-8",
                "Cookie": cookie_string,
                "DNT": "1",
                "Origin": "https://autoshopee.com",
                "Pragma": "no-cache",
                "Priority": "u=1, i",
                "Referer": "https://autoshopee.com/core/shopee/product/search",
                "Sec-Ch-Ua": '"Chromium";v="120", "Google Chrome";v="120", ".Not/A)Brand";v="99"',
                "Sec-Ch-Ua-Mobile": "?0",
                "Sec-Ch-Ua-Platform": '"Windows"',
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-origin",
                "Upgrade-Insecure-Requests": "1",
                "User-Agent": selected_ua,
                "X-Csrf-Token": token or cookie_dict.get('_csrf', ''),
                "X-Requested-With": "XMLHttpRequest"
            }

            # Lưu vào cache với thời gian hết hạn là 1 giờ
            _auth_info['cookies'] = cookie_dict
            _auth_info['headers'] = headers
            _auth_info['expiry_time'] = current_time + 3600  # 1 giờ

            return cookie_dict
        finally:
            driver.quit()

def get_auth_headers():
    """Lấy headers xác thực đã lưu trong cache"""
    with _auth_lock:
        return _auth_info['headers'] if _auth_info['headers'] else None

def create_varied_headers(base_headers):
    """
    Tạo headers với variation nhỏ để tránh fingerprinting

    Args:
        base_headers: Headers gốc từ authentication

    Returns:
        dict: Headers đã được variation
    """
    if not base_headers:
        return None

    # Copy headers gốc
    varied_headers = base_headers.copy()

    # Chỉ thay đổi User-Agent với tần suất thấp hơn để ổn định
    if random.random() < 0.3:  # 30% chance thay đổi UA
        varied_headers["User-Agent"] = random.choice(USER_AGENT_POOL)

    # Chỉ thay đổi Accept-Language với tần suất thấp hơn
    if random.random() < 0.2:  # 20% chance thay đổi Accept-Language
        varied_headers["Accept-Language"] = random.choice(ACCEPT_LANGUAGE_POOL)

    return varied_headers

def fetch_shop_products(match_id, session, headers, timeout=30, max_retries=3, log_callback=None):
    """
    Lấy dữ liệu sản phẩm cho một shop cụ thể với cơ chế retry cho shop 0 sản phẩm

    Args:
        match_id: ID của shop cần lấy dữ liệu
        session: Session requests để tái sử dụng kết nối
        headers: Headers xác thực
        timeout: Thời gian timeout
        max_retries: Số lần thử lại tối đa cho API calls
        log_callback: Callback để ghi log

    Returns:
        list: Danh sách sản phẩm của shop
    """
    def log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)

    # Cơ chế retry cho shop có 0 sản phẩm
    max_shop_retries = 2  # Retry tối đa 2 lần cho shop 0 sản phẩm
    shop_retry_count = 0

    while shop_retry_count <= max_shop_retries:
        if shop_retry_count == 0:
            log(f"\n🏪 Đang lấy dữ liệu từ shop ID: {match_id}")
        else:
            log(f"\n🔄 Retry lần {shop_retry_count} cho shop ID: {match_id}")

        # Thực hiện lấy dữ liệu shop
        shop_products = _fetch_single_shop_attempt(match_id, session, headers, timeout, max_retries, log_callback)

        # Nếu có sản phẩm hoặc đã retry đủ số lần
        if len(shop_products) > 0 or shop_retry_count >= max_shop_retries:
            break

        # Nếu 0 sản phẩm và chưa retry đủ
        shop_retry_count += 1
        if shop_retry_count <= max_shop_retries:
            retry_delay = random.uniform(15, 25)  # Delay lớn hơn cho shop retry
            log(f"⏳ Shop trả về 0 sản phẩm. Đợi {retry_delay:.1f}s trước khi retry...")
            time.sleep(retry_delay)

    # Log kết quả cuối cùng (chỉ log này mới được UI đếm)
    if len(shop_products) > 0:
        log(f"✅ Shop ID {match_id}: Hoàn thành với {len(shop_products)} sản phẩm")
    else:
        if shop_retry_count > 0:
            log(f"ℹ️ Shop ID {match_id}: Không có sản phẩm nào (đã retry {shop_retry_count} lần)")
        else:
            log(f"ℹ️ Shop ID {match_id}: Không có sản phẩm nào")

    return shop_products

def _fetch_single_shop_attempt(match_id, session, headers, timeout=30, max_retries=3, log_callback=None):
    """
    Thực hiện một lần thử lấy dữ liệu từ shop (helper function)

    Returns:
        list: Danh sách sản phẩm của shop trong lần thử này
    """
    def log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)

    url = "https://autoshopee.com/core/shopee/product/search/get"

    # API Autoshopee giới hạn mỗi request chỉ trả về tối đa 40 sản phẩm
    page_limit = 40
    page_from = 0
    shop_products = []

    # Theo dõi số lần không có sản phẩm mới để tránh vòng lặp vô hạn
    empty_responses = 0
    max_empty_responses = 3  # Dừng sau 3 lần không có sản phẩm mới
    max_pages = 1000  # Giới hạn số trang tối đa để tránh vòng lặp vô hạn
    current_page = 0

    while empty_responses < max_empty_responses and current_page < max_pages:
        current_page += 1
        payload = {
            "match_id": str(match_id),
            "page_type": "shop",
            "querySoldOut": True,
            "order": "desc",
            "sort_by": "ctime",
            "pageFrom": page_from,
            "pageTo": page_from + page_limit - 1,
            "pageLimit": page_limit
        }

        # Thêm cơ chế retry
        retry_count = 0
        success = False
        data = None

        while retry_count < max_retries and not success:
            try:
                # Tạo headers với variation cho mỗi request (fallback về headers gốc nếu lỗi)
                try:
                    varied_headers = create_varied_headers(headers)
                except:
                    varied_headers = headers  # Fallback về headers gốc

                # Gửi request với timeout và varied headers
                resp = session.post(url, headers=varied_headers, json=payload, timeout=timeout)

                if resp.status_code == 200:
                    try:
                        # Kiểm tra content trước khi parse JSON
                        response_text = resp.text.strip()
                        if not response_text:
                            # Response rỗng có thể là lỗi tạm thời, không phải tín hiệu kết thúc
                            log(f"⚠️ Response rỗng (lần thử {retry_count + 1}/{max_retries}) - có thể là rate limiting")
                        elif not response_text.startswith('{') and not response_text.startswith('['):
                            log(f"❌ Response không phải JSON (lần thử {retry_count + 1}/{max_retries}): {response_text[:100]}...")
                        else:
                            data = resp.json()
                            if data.get("success", False):
                                success = True
                            else:
                                log(f"❌ API lỗi (lần thử {retry_count + 1}/{max_retries}): {data}")
                    except Exception as e:
                        log(f"❌ Lỗi giải mã JSON (lần thử {retry_count + 1}/{max_retries}): {e}")
                        log(f"📄 Response content: {resp.text[:200]}...")
                else:
                    log(f"❌ HTTP lỗi (lần thử {retry_count + 1}/{max_retries}): {resp.status_code}")
                    log(f"📄 Response content: {resp.text[:200]}...")

            except requests.exceptions.Timeout:
                log(f"⏱️ Timeout khi gọi API (lần thử {retry_count + 1}/{max_retries})")
            except requests.exceptions.ConnectionError:
                log(f"🔌 Lỗi kết nối khi gọi API (lần thử {retry_count + 1}/{max_retries})")
            except Exception as e:
                log(f"❌ Lỗi không xác định khi gọi API (lần thử {retry_count + 1}/{max_retries}): {e}")

            if not success:
                retry_count += 1
                if retry_count < max_retries:
                    # Exponential backoff với jitter để tránh thundering herd
                    base_wait = 3 ** retry_count  # 3, 9, 27 giây... (tăng từ 2)
                    jitter = random.uniform(0, 2)  # Thêm random 0-2 giây (tăng từ 1)
                    wait_time = base_wait + jitter
                    log(f"⏳ Đợi {wait_time:.1f} giây trước khi thử lại...")
                    time.sleep(wait_time)

        # Nếu đã retry đủ số lần mà vẫn thất bại
        if not success:
            log("❌ Đã thử lại tối đa số lần nhưng vẫn thất bại. Dừng việc lấy dữ liệu.")
            break

        # Xử lý dữ liệu khi thành công
        products = data.get("data", [])
        product_count = len(products)

        # Kiểm tra nếu có dữ liệu trả về
        if product_count > 0:
            log(f"📦 Trang {current_page}: Sản phẩm từ {page_from} đến {page_from+product_count-1}: {product_count} sản phẩm")
            shop_products.extend(products)
            # Reset bộ đếm khi có sản phẩm
            empty_responses = 0
        else:
            empty_responses += 1
            log(f"ℹ️ Trang {current_page}: Không có sản phẩm ({empty_responses}/{max_empty_responses})")

            # Chỉ thoát khi đã thử đủ số lần liên tiếp không có sản phẩm
            if empty_responses >= max_empty_responses:
                # Không log kết quả ở đây nữa - để main function xử lý
                break

        # Tiến đến trang kế tiếp - chắc chắn tăng theo số lượng 40
        page_from += page_limit

        # Smart delay giữa các request để tránh rate limiting
        request_delay = random.uniform(2.0, 5.0)  # 2-5 giây random - tăng để tránh rate limiting
        log(f"⏳ Đợi {request_delay:.1f}s trước request tiếp theo...")
        time.sleep(request_delay)

    # Trả về kết quả mà không log (để main function quyết định)
    return shop_products

def get_optimal_excel_writer(output_filename, log_callback, num_shops):
    """
    Chọn Excel Writer tối ưu - SQLite Buffer cho tốc độ tối đa

    Args:
        output_filename: Tên file Excel đầu ra
        log_callback: Callback để ghi log
        num_shops: Số lượng shop cần xử lý

    Returns:
        SQLiteBufferExcelWriter instance (tối ưu cho 8 threads)
    """
    def log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)

    # SQLITE BUFFER STRATEGY: Tối ưu cho 8 threads và large datasets
    log(f"🗄️ SQLITE BUFFER: SQLiteBufferExcelWriter cho {num_shops} shop (tối ưu 8 threads)")
    log(f"🔍 DEBUG: Đang khởi tạo SQLiteBufferExcelWriter...")
    return SQLiteBufferExcelWriter(output_filename, log_callback)

def fetch_all_products_from_multiple_shops_streaming(username, password, match_ids, output_filename, headless=True, timeout=30, max_retries=3, log_callback=None, max_workers=8):
    """
    Lấy dữ liệu từ nhiều shop và ghi Excel streaming (tối ưu hiệu suất)

    Args:
        username: Tên đăng nhập
        password: Mật khẩu
        match_ids: Danh sách ID shop
        output_filename: Tên file Excel đầu ra
        headless: Chạy ở chế độ headless
        timeout: Thời gian timeout
        max_retries: Số lần thử lại tối đa
        log_callback: Callback để ghi log
        max_workers: Số luồng tối đa chạy song song

    Returns:
        bool: True nếu thành công
    """
    def log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)

    try:
        # Đăng nhập trước để lấy cookies và headers
        log("🔑 Đang đăng nhập vào Autoshopee...")
        get_logged_in_cookies(username, password, headless=headless)

        # Lấy headers đã được chuẩn bị
        headers = get_auth_headers()

        if not headers:
            raise Exception("❌ Không thể lấy thông tin xác thực!")

        log("✅ Đăng nhập thành công.")

        # Khởi tạo SIÊU NHANH Excel writer - tự động chọn strategy tối ưu
        excel_writer = get_optimal_excel_writer(output_filename, log_callback, len(match_ids))

        # Tạo và cấu hình session requests để tái sử dụng
        session = requests.Session()
        shop_count = 0
        total_products = 0

        # Sử dụng ThreadPoolExecutor để chạy đa luồng
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Bắt đầu các tác vụ lấy dữ liệu cho từng shop và lưu futures
            futures = {
                executor.submit(
                    fetch_shop_products,
                    match_id,
                    session,
                    headers,
                    timeout,
                    max_retries,
                    log_callback
                ): match_id for match_id in match_ids
            }

            # Thu thập kết quả khi các tác vụ hoàn thành và ghi ngay vào Excel
            for i, future in enumerate(concurrent.futures.as_completed(futures)):
                match_id = futures[future]
                try:
                    shop_products = future.result()
                    if shop_products:
                        # Gửi dữ liệu shop vào writer (strategy tùy thuộc vào writer type)
                        excel_writer.append_products(shop_products)
                        total_products += len(shop_products)
                        log(f"✅ Shop ID {match_id}: Hoàn thành với {len(shop_products)} sản phẩm")

                    shop_count += 1
                    log(f"🔄 Tiến độ: {shop_count}/{len(match_ids)} shop đã hoàn thành")

                    # Session refresh mỗi 100 shop để reset fingerprint
                    if shop_count % 100 == 0:
                        log(f"🔄 Refresh session sau {shop_count} shop để tránh detection...")
                        session.close()
                        session = requests.Session()

                        # Thêm delay lớn hơn sau khi refresh session
                        session_refresh_delay = random.uniform(8, 15)
                        log(f"⏳ Đợi {session_refresh_delay:.1f}s sau khi refresh session...")
                        time.sleep(session_refresh_delay)

                    # Delay giữa các shop để tránh quá aggressive
                    elif i < len(match_ids) - 1:  # Không delay ở shop cuối cùng
                        shop_delay = random.uniform(8, 15)  # 8-15 giây
                        log(f"⏳ Đợi {shop_delay:.1f}s trước khi xử lý shop tiếp theo...")
                        time.sleep(shop_delay)

                except Exception as e:
                    log(f"❌ Lỗi khi xử lý shop ID {match_id}: {str(e)}")

        # Hoàn tất và đóng file Excel với SQLite Buffer
        success = excel_writer.finalize()
        if success:
            log(f"✅ Hoàn thành! Đã lưu {total_products} sản phẩm vào {output_filename}")
            return True
        else:
            log(f"❌ Lỗi khi finalize SQLite Buffer Writer")
            return False

    except Exception as e:
        log(f"❌ Lỗi: {str(e)}")
        return False

def fetch_all_products_from_multiple_shops(username, password, match_ids, headless=True, timeout=30, max_retries=3, log_callback=None, max_workers=8):
    """
    Lấy dữ liệu từ nhiều shop sử dụng đa luồng (phương pháp cũ - để backward compatibility)

    Args:
        username: Tên đăng nhập
        password: Mật khẩu
        match_ids: Danh sách ID shop
        headless: Chạy ở chế độ headless
        timeout: Thời gian timeout
        max_retries: Số lần thử lại tối đa
        log_callback: Callback để ghi log
        max_workers: Số luồng tối đa chạy song song

    Returns:
        list: Danh sách tất cả sản phẩm từ các shop
    """
    def log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)

    # Danh sách để lưu tất cả sản phẩm từ các shop
    all_shops_products = []

    try:
        # Đăng nhập trước để lấy cookies và headers
        log("🔑 Đang đăng nhập vào Autoshopee...")
        get_logged_in_cookies(username, password, headless=headless)

        # Lấy headers đã được chuẩn bị
        headers = get_auth_headers()

        if not headers:
            raise Exception("❌ Không thể lấy thông tin xác thực!")

        log("✅ Đăng nhập thành công.")

        # Tạo và cấu hình session requests để tái sử dụng
        session = requests.Session()
        shop_count = 0

        # Sử dụng ThreadPoolExecutor để chạy đa luồng với 3 workers
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Bắt đầu các tác vụ lấy dữ liệu cho từng shop và lưu futures
            futures = {
                executor.submit(
                    fetch_shop_products,
                    match_id,
                    session,
                    headers,
                    timeout,
                    max_retries,
                    log_callback
                ): match_id for match_id in match_ids
            }

            # Thu thập kết quả khi các tác vụ hoàn thành
            for i, future in enumerate(concurrent.futures.as_completed(futures)):
                match_id = futures[future]
                try:
                    shop_products = future.result()
                    all_shops_products.extend(shop_products)
                    shop_count += 1
                    log(f"🔄 Tiến độ: {i+1}/{len(match_ids)} shop đã hoàn thành")

                    # Session refresh mỗi 100 shop để reset fingerprint (tăng từ 75 để ổn định hơn)
                    if shop_count % 100 == 0:
                        log(f"🔄 Refresh session sau {shop_count} shop để tránh detection...")
                        session.close()
                        session = requests.Session()

                        # Thêm delay lớn hơn sau khi refresh session
                        session_refresh_delay = random.uniform(8, 15)
                        log(f"⏳ Đợi {session_refresh_delay:.1f}s sau khi refresh session...")
                        time.sleep(session_refresh_delay)

                    # Delay giữa các shop để tránh quá aggressive
                    elif i < len(match_ids) - 1:  # Không delay ở shop cuối cùng
                        shop_delay = random.uniform(8, 15)  # 8-15 giây
                        log(f"⏳ Đợi {shop_delay:.1f}s trước khi xử lý shop tiếp theo...")
                        time.sleep(shop_delay)

                except Exception as e:
                    log(f"❌ Lỗi khi xử lý shop ID {match_id}: {str(e)}")

    except Exception as e:
        log(f"❌ Lỗi: {str(e)}")

    return all_shops_products

class StreamingExcelWriter:
    """
    Class để ghi Excel streaming - ghi dần dữ liệu thay vì chờ tất cả
    """
    def __init__(self, filename, log_callback=None):
        self.filename = filename
        self.log_callback = log_callback
        self.wb = Workbook()
        self.ws = self.wb.active
        self.current_row = 1
        self.headers_written = False

    def log(self, message):
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def append_products(self, products):
        """Thêm danh sách sản phẩm vào Excel"""
        if not products:
            return

        try:
            # Làm sạch dữ liệu
            clean_products = []
            for product in products:
                clean_product = {}
                for key, value in product.items():
                    if isinstance(value, str):
                        clean_product[key] = clean_text_for_excel(value)
                    else:
                        clean_product[key] = value
                clean_products.append(clean_product)

            # Tạo DataFrame
            df = pd.json_normalize(clean_products)

            # Tạo DataFrame với cấu trúc cột theo yêu cầu
            new_df = self._format_dataframe(df)

            # Ghi headers nếu chưa ghi
            if not self.headers_written:
                self._write_headers(new_df)
                self.headers_written = True

            # Ghi dữ liệu
            self._write_data(new_df)

        except Exception as e:
            self.log(f"❌ Lỗi khi ghi dữ liệu vào Excel: {str(e)}")

    def _format_dataframe(self, df):
        """Format DataFrame theo yêu cầu"""
        # Tạo dữ liệu cho 3 cột đầu tiên
        key_col = df['id'].astype(str) + '_' + df['shopID'].astype(str)
        sku_col = df['id']
        id_col = df['id']

        # Tạo DataFrame với cấu trúc mới
        new_df = pd.DataFrame({
            '': key_col,  # Cột trống 1
            ' ': sku_col,  # Cột trống 2 (dùng space để phân biệt)
            '  ': id_col   # Cột trống 3 (dùng 2 spaces)
        })

        # Thêm các cột còn lại
        new_df['Link sản phẩm'] = df['linkProduct'] if 'linkProduct' in df.columns else ''
        new_df['Link Shop'] = df['linkShop'] if 'linkShop' in df.columns else ''
        new_df['Tên sản phẩm'] = df['name'] if 'name' in df.columns else ''
        new_df['Thương hiệu'] = df['brand'] if 'brand' in df.columns else ''
        new_df['Mô tả'] = df['description'] if 'description' in df.columns else ''
        new_df['Ngày tạo'] = df['timeCreate'] if 'timeCreate' in df.columns else ''
        new_df['Mã Shop'] = df['itemID'] if 'itemID' in df.columns else ''
        new_df['Mã Sản phẩm'] = df['shopID'] if 'shopID' in df.columns else ''
        new_df['Chuyên mục'] = df['categoryMain'] if 'categoryMain' in df.columns else ''
        new_df['Chuyên mục.1'] = df['categoryTree'] if 'categoryTree' in df.columns else ''
        new_df['Giá hiện tại'] = df['price'] if 'price' in df.columns else ''
        new_df['Giá thấp nhất'] = df['priceMin'] if 'priceMin' in df.columns else ''
        new_df['Giá cao nhất'] = df['priceMax'] if 'priceMax' in df.columns else ''
        new_df['Giảm giá'] = df['discount'] if 'discount' in df.columns else ''
        new_df['Tồn kho'] = df['stock'] if 'stock' in df.columns else ''
        new_df['Cân nặng'] = df['weight'] if 'weight' in df.columns else ''
        new_df['Hình ảnh'] = df['image'] if 'image' in df.columns else ''
        new_df['Số Đánh giá'] = df['cmtCount'] if 'cmtCount' in df.columns else ''
        new_df['Số lượt xem'] = df['viewCount'] if 'viewCount' in df.columns else ''
        new_df['Số thích'] = df['likedCount'] if 'likedCount' in df.columns else ''
        new_df['Điểm đánh giá'] = df['itemRating'] if 'itemRating' in df.columns else ''
        new_df['Đã bán 30 ngày'] = df['sold_30day'] if 'sold_30day' in df.columns else ''
        new_df['Doanh số 30 ngày'] = df['sale_30day'] if 'sale_30day' in df.columns else ''
        new_df['Đã bán toàn thời gian'] = df['sold_alltime'] if 'sold_alltime' in df.columns else ''
        new_df['Doanh số toàn thời gian'] = df['sale_alltime'] if 'sale_alltime' in df.columns else ''
        new_df['Vị trí'] = df['location'] if 'location' in df.columns else ''
        new_df['Video'] = df['video'] if 'video' in df.columns else ''

        return new_df

    def _write_headers(self, df):
        """Ghi headers vào Excel"""
        for c_idx, column in enumerate(df.columns, 1):
            # Đặt header trống cho 3 cột đầu tiên
            if c_idx <= 3:
                header_value = ""
            else:
                header_value = column

            self.ws.cell(row=self.current_row, column=c_idx, value=header_value)
            self.ws.cell(row=self.current_row, column=c_idx).font = Font(bold=False)

        self.current_row += 1

    def _write_data(self, df):
        """Ghi dữ liệu vào Excel"""
        for _, row in df.iterrows():
            for c_idx, value in enumerate(row, 1):
                try:
                    if isinstance(value, str):
                        value = clean_text_for_excel(value)
                    self.ws.cell(row=self.current_row, column=c_idx, value=value)
                except Exception as cell_error:
                    self.log(f"⚠️ Lỗi khi ghi cell ({self.current_row}, {c_idx}): {str(cell_error)}")
                    self.ws.cell(row=self.current_row, column=c_idx, value="[Lỗi dữ liệu]")

            self.current_row += 1

    def finalize(self):
        """Hoàn tất và lưu file Excel"""
        try:
            self.wb.save(self.filename)
            self.log(f"📁 Đã lưu file Excel: {self.filename}")
        except Exception as e:
            self.log(f"❌ Lỗi khi lưu file Excel: {str(e)}")

def clean_text_for_excel(text):
    """
    Làm sạch văn bản để tránh lỗi khi ghi vào Excel mà vẫn giữ nguyên nội dung

    Args:
        text: Văn bản cần làm sạch

    Returns:
        str: Văn bản đã được làm sạch
    """
    if not isinstance(text, str):
        return text

    # Thay thế các ký tự không hợp lệ bằng ký tự tương đương hoặc Unicode
    # Sử dụng regex để tìm và thay thế các ký tự không hợp lệ trong Excel
    return ILLEGAL_CHARACTERS_RE.sub('', text)

class ProductionParallelWriter:
    """
    Production Parallel Writer - ULTIMATE SOLUTION cho 1000+ shop
    - Background thread để ghi Excel song song với scraping
    - Queue-based architecture để không block scraping
    - Memory management cho long-running process
    - Fault tolerance và recovery
    """

    def __init__(self, excel_path, log_callback=None):
        """
        Khởi tạo Production Parallel Writer

        Args:
            excel_path: Đường dẫn file Excel đầu ra
            log_callback: Callback để ghi log
        """
        self.excel_path = excel_path
        self.log_callback = log_callback

        # PARALLEL ARCHITECTURE
        import queue
        import threading
        self.write_queue = queue.Queue(maxsize=50)  # Buffer 50 batches
        self.writer_thread = None
        self.stop_writing = threading.Event()

        # Performance tracking
        self.total_products = 0
        self.batches_written = 0

        # Thread-safe lock
        self.lock = threading.Lock()

        # Start background writer thread
        self._start_background_writer()

        self.log("🚀 Khởi tạo Production Parallel Writer (ULTIMATE PERFORMANCE)")

    def log(self, message):
        """Helper function để ghi log"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def _start_background_writer(self):
        """Khởi động background thread để ghi Excel"""
        self.writer_thread = threading.Thread(target=self._background_excel_writer, daemon=True)
        self.writer_thread.start()
        self.log("🔥 Background Excel writer thread started")

    def _background_excel_writer(self):
        """Background thread để ghi Excel liên tục"""
        import queue  # Import trong method để tránh lỗi
        try:
            # Khởi tạo Excel workbook trong background thread
            workbook = Workbook(write_only=True)
            worksheet = workbook.create_sheet()
            headers_written = False

            while not self.stop_writing.is_set():
                try:
                    # Lấy batch từ queue (timeout 1 giây)
                    batch_data = self.write_queue.get(timeout=1.0)

                    if batch_data is None:  # Poison pill để stop
                        break

                    # Ghi headers nếu chưa ghi
                    if not headers_written:
                        headers = ['', ' ', '  ']  # 3 cột đầu trống
                        headers.extend([
                            'Link sản phẩm', 'Link Shop', 'Tên sản phẩm', 'Thương hiệu', 'Mô tả',
                            'Ngày tạo', 'Mã Shop', 'Mã Sản phẩm', 'Chuyên mục', 'Chuyên mục.1',
                            'Giá hiện tại', 'Giá thấp nhất', 'Giá cao nhất', 'Giảm giá', 'Tồn kho', 'Cân nặng',
                            'Hình ảnh', 'Số Đánh giá', 'Số lượt xem', 'Số thích', 'Điểm đánh giá',
                            'Đã bán 30 ngày', 'Doanh số 30 ngày', 'Đã bán toàn thời gian', 'Doanh số toàn thời gian',
                            'Vị trí', 'Video'
                        ])
                        worksheet.append(headers)
                        headers_written = True

                    # Ghi batch data
                    for row in batch_data:
                        worksheet.append(row)

                    self.batches_written += 1
                    self.write_queue.task_done()

                    # Log progress mỗi 10 batches
                    if self.batches_written % 10 == 0:
                        self.log(f"📝 Background writer: {self.batches_written} batches written")

                except queue.Empty:
                    continue  # Timeout, tiếp tục loop
                except Exception as e:
                    self.log(f"❌ Background writer error: {str(e)}")

            # Lưu file cuối cùng
            self.log("💾 Background writer: Saving final Excel file...")
            workbook.save(self.excel_path)
            self.log(f"✅ Background writer: Saved {self.total_products:,} products to Excel")

        except Exception as e:
            self.log(f"❌ Fatal background writer error: {str(e)}")

    def append_products(self, products):
        """
        Append products với PARALLEL processing

        Args:
            products: List các sản phẩm từ API
        """
        if not products:
            return

        with self.lock:
            self.total_products += len(products)

        # Chuẩn bị data cho background writer
        field_order = ['linkProduct', 'linkShop', 'name', 'brand', 'description',
                      'timeCreate', 'itemID', 'shopID', 'categoryMain', 'categoryTree',
                      'price', 'priceMin', 'priceMax', 'discount', 'stock', 'weight',
                      'image', 'cmtCount', 'viewCount', 'likedCount', 'itemRating',
                      'sold_30day', 'sale_30day', 'sold_alltime', 'sale_alltime',
                      'location', 'video']

        batch_data = []
        for product in products:
            # 3 cột đầu
            key_value = f"{product.get('id', '')}__{product.get('shopID', '')}"
            row = [key_value, product.get('id', ''), product.get('id', '')]

            # Các cột còn lại
            for field_name in field_order:
                value = product.get(field_name, '')
                if isinstance(value, str):
                    value = clean_text_for_excel(value)
                row.append(value)

            batch_data.append(row)

        # Gửi vào queue cho background writer (NON-BLOCKING)
        try:
            self.write_queue.put(batch_data, timeout=0.1)  # 100ms timeout
            # Log ngắn gọn
            if self.total_products % 1000 == 0:
                self.log(f"🚀 PARALLEL: {self.total_products:,} sản phẩm → background writer")
        except:  # Catch any queue exception
            self.log("⚠️ Write queue full, background writer đang bận")

    def finalize(self):
        """
        Hoàn tất quá trình PARALLEL

        Returns:
            bool: True nếu thành công
        """
        try:
            # Gửi poison pill để stop background writer
            self.write_queue.put(None)
            self.stop_writing.set()

            # Đợi background writer hoàn thành
            self.log("⏳ Đợi background writer hoàn thành...")
            if self.writer_thread:
                self.writer_thread.join(timeout=60)  # Timeout 60 giây

            self.log(f"✅ PARALLEL COMPLETE! {self.total_products:,} sản phẩm đã được ghi")
            return True

        except Exception as e:
            self.log(f"❌ PARALLEL ERROR: {str(e)}")
            return False


class ProductionStreamingWriter:
    """
    Production Streaming Writer - Thiết kế đặc biệt cho PRODUCTION WORKLOAD
    - 1000+ shop IDs
    - 160k-200k sản phẩm
    - Thời gian chạy dài (30+ phút)
    - Memory efficient + Ultra fast writing
    """

    def __init__(self, excel_path, log_callback=None):
        """
        Khởi tạo Production Streaming Writer

        Args:
            excel_path: Đường dẫn file Excel đầu ra
            log_callback: Callback để ghi log
        """
        self.excel_path = excel_path
        self.log_callback = log_callback

        # PRODUCTION OPTIMIZATIONS
        self.workbook = Workbook(write_only=True)  # Write-only mode cho BIG DATA
        self.worksheet = self.workbook.create_sheet()

        # MEGA BUFFER cho production (10x lớn hơn)
        self.write_buffer = []
        self.buffer_size = 5000  # 5000 products per batch (tối ưu cho 160k+ sản phẩm)

        # Performance tracking
        self.total_products = 0
        self.batch_count = 0
        self.headers_written = False

        # Thread-safe lock
        self.lock = threading.Lock()

        # Memory management
        import gc
        self.gc_frequency = 10  # Garbage collect mỗi 10 batches

        self.log("🏭 Khởi tạo Production Streaming Writer (BIG DATA optimized)")

    def log(self, message):
        """Helper function để ghi log"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def append_products(self, products):
        """
        Append products với PRODUCTION optimizations

        Args:
            products: List các sản phẩm từ API
        """
        if not products:
            return

        with self.lock:
            # Ghi headers nếu chưa ghi (chỉ 1 lần)
            if not self.headers_written:
                self._write_headers_production()
                self.headers_written = True

            # Thêm vào mega buffer
            self.write_buffer.extend(products)
            self.total_products += len(products)

            # MEGA BATCH WRITE khi buffer đầy
            if len(self.write_buffer) >= self.buffer_size:
                self._flush_mega_batch()

            # Log ngắn gọn để không spam
            if self.total_products % 1000 == 0:
                self.log(f"🏭 PRODUCTION: {self.total_products:,} sản phẩm đã xử lý")

    def _write_headers_production(self):
        """Ghi headers tối ưu cho production"""
        headers = ['', ' ', '  ']  # 3 cột đầu trống
        headers.extend([
            'Link sản phẩm', 'Link Shop', 'Tên sản phẩm', 'Thương hiệu', 'Mô tả',
            'Ngày tạo', 'Mã Shop', 'Mã Sản phẩm', 'Chuyên mục', 'Chuyên mục.1',
            'Giá hiện tại', 'Giá thấp nhất', 'Giá cao nhất', 'Giảm giá', 'Tồn kho', 'Cân nặng',
            'Hình ảnh', 'Số Đánh giá', 'Số lượt xem', 'Số thích', 'Điểm đánh giá',
            'Đã bán 30 ngày', 'Doanh số 30 ngày', 'Đã bán toàn thời gian', 'Doanh số toàn thời gian',
            'Vị trí', 'Video'
        ])

        # Write-only mode: append row trực tiếp
        self.worksheet.append(headers)

    def _flush_mega_batch(self):
        """Flush mega batch với optimizations cho BIG DATA"""
        if not self.write_buffer:
            return

        self.batch_count += 1
        batch_size = len(self.write_buffer)

        # Pre-defined field order để tránh dict lookup
        field_order = ['linkProduct', 'linkShop', 'name', 'brand', 'description',
                      'timeCreate', 'itemID', 'shopID', 'categoryMain', 'categoryTree',
                      'price', 'priceMin', 'priceMax', 'discount', 'stock', 'weight',
                      'image', 'cmtCount', 'viewCount', 'likedCount', 'itemRating',
                      'sold_30day', 'sale_30day', 'sold_alltime', 'sale_alltime',
                      'location', 'video']

        # BULK APPEND: Chuẩn bị tất cả rows trước khi ghi
        rows_to_append = []
        for product in self.write_buffer:
            # 3 cột đầu
            key_value = f"{product.get('id', '')}__{product.get('shopID', '')}"
            row = [key_value, product.get('id', ''), product.get('id', '')]

            # Các cột còn lại
            for field_name in field_order:
                value = product.get(field_name, '')
                if isinstance(value, str):
                    value = clean_text_for_excel(value)
                row.append(value)

            rows_to_append.append(row)

        # MEGA WRITE: Ghi tất cả rows cùng lúc
        for row in rows_to_append:
            self.worksheet.append(row)

        self.log(f"🚀 MEGA BATCH #{self.batch_count}: Đã ghi {batch_size:,} sản phẩm (tổng: {self.total_products:,})")

        # Clear buffer
        self.write_buffer = []

        # MEMORY MANAGEMENT: Garbage collect định kỳ cho long-running process
        if self.batch_count % self.gc_frequency == 0:
            import gc
            gc.collect()
            self.log(f"🧹 Memory cleanup sau {self.batch_count} batches")

    def finalize(self):
        """
        Hoàn tất quá trình cho PRODUCTION

        Returns:
            bool: True nếu thành công
        """
        try:
            with self.lock:
                # Ghi buffer cuối cùng
                if self.write_buffer:
                    self._flush_mega_batch()

                # PRODUCTION SAVE với progress tracking
                self.log(f"💾 PRODUCTION SAVE: Đang lưu {self.total_products:,} sản phẩm...")

                # Save với write-only mode (tối ưu cho BIG DATA)
                self.workbook.save(self.excel_path)

                self.log(f"✅ PRODUCTION COMPLETE! Đã lưu {self.total_products:,} sản phẩm vào {self.excel_path}")
                self.log(f"📊 Thống kê: {self.batch_count} mega batches, {self.buffer_size} products/batch")

                return True

        except Exception as e:
            self.log(f"❌ PRODUCTION ERROR: {str(e)}")
            return False


class MemoryExcelWriter:
    """
    Memory-based Excel Writer - Lưu tất cả data trong memory, chỉ ghi file 1 lần cuối
    Giải pháp triệt để cho vấn đề tốc độ ghi chậm
    """

    def __init__(self, excel_path, log_callback=None):
        """
        Khởi tạo Memory Excel Writer

        Args:
            excel_path: Đường dẫn file Excel đầu ra
            log_callback: Callback để ghi log
        """
        self.excel_path = excel_path
        self.log_callback = log_callback

        # Memory storage - lưu tất cả data trong RAM
        self.all_products = []  # List chứa tất cả products
        self.total_products = 0

        # Thread-safe lock
        self.memory_lock = threading.Lock()

        self.log("🧠 Khởi tạo Memory Excel Writer (siêu tốc độ - chỉ ghi file 1 lần)")
        self.log("🔍 DEBUG: MemoryExcelWriter được khởi tạo thành công")

    def log(self, message):
        """Helper function để ghi log"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def append_products(self, products):
        """
        Thêm danh sách sản phẩm vào memory (siêu nhanh)

        Args:
            products: List các sản phẩm từ API
        """
        if not products:
            return

        with self.memory_lock:
            # Chỉ append vào memory - SIÊU NHANH (không ghi file ngay)
            self.all_products.extend(products)
            self.total_products += len(products)
            # Log rõ ràng về memory mode
            self.log(f"💾 MEMORY MODE: +{len(products)} sản phẩm vào RAM (tổng: {self.total_products}) - CHƯA GHI FILE")

    def finalize(self):
        """
        Hoàn tất: Ghi tất cả data từ memory vào Excel 1 lần duy nhất

        Returns:
            bool: True nếu thành công
        """
        try:
            if not self.all_products:
                self.log("⚠️ Không có dữ liệu để ghi")
                return False

            self.log(f"🚀 Bắt đầu ghi {self.total_products} sản phẩm từ RAM vào Excel...")

            # Tạo workbook
            wb = Workbook()
            ws = wb.active

            # Ghi headers
            self._write_headers_fast(ws)

            # Ghi tất cả data cùng lúc - BULK OPERATION
            self._write_all_data_bulk(ws)

            # Lưu file 1 lần duy nhất
            self.log("💾 Đang lưu file Excel...")
            wb.save(self.excel_path)
            self.log(f"✅ HOÀN TẤT! Đã lưu {self.total_products} sản phẩm vào {self.excel_path}")

            return True

        except Exception as e:
            self.log(f"❌ Lỗi trong finalize: {str(e)}")
            return False

    def _write_headers_fast(self, ws):
        """Ghi headers nhanh"""
        headers = ['', ' ', '  ']  # 3 cột đầu trống
        headers.extend([
            'Link sản phẩm', 'Link Shop', 'Tên sản phẩm', 'Thương hiệu', 'Mô tả',
            'Ngày tạo', 'Mã Shop', 'Mã Sản phẩm', 'Chuyên mục', 'Chuyên mục.1',
            'Giá hiện tại', 'Giá thấp nhất', 'Giá cao nhất', 'Giảm giá', 'Tồn kho', 'Cân nặng',
            'Hình ảnh', 'Số Đánh giá', 'Số lượt xem', 'Số thích', 'Điểm đánh giá',
            'Đã bán 30 ngày', 'Doanh số 30 ngày', 'Đã bán toàn thời gian', 'Doanh số toàn thời gian',
            'Vị trí', 'Video'
        ])

        for col_idx, header in enumerate(headers, 1):
            ws.cell(row=1, column=col_idx, value=header)
            ws.cell(row=1, column=col_idx).font = Font(bold=False)

    def _write_all_data_bulk(self, ws):
        """Ghi tất cả data bulk - SIÊU NHANH"""
        field_order = ['linkProduct', 'linkShop', 'name', 'brand', 'description',
                      'timeCreate', 'itemID', 'shopID', 'categoryMain', 'categoryTree',
                      'price', 'priceMin', 'priceMax', 'discount', 'stock', 'weight',
                      'image', 'cmtCount', 'viewCount', 'likedCount', 'itemRating',
                      'sold_30day', 'sale_30day', 'sold_alltime', 'sale_alltime',
                      'location', 'video']

        for row_idx, product in enumerate(self.all_products, 2):  # Bắt đầu từ row 2
            # 3 cột đầu
            key_value = f"{product.get('id', '')}__{product.get('shopID', '')}"
            ws.cell(row=row_idx, column=1, value=key_value)
            ws.cell(row=row_idx, column=2, value=product.get('id', ''))
            ws.cell(row=row_idx, column=3, value=product.get('id', ''))

            # Các cột còn lại
            for col_offset, field_name in enumerate(field_order):
                col_idx = 4 + col_offset
                value = product.get(field_name, '')
                if isinstance(value, str):
                    value = clean_text_for_excel(value)
                ws.cell(row=row_idx, column=col_idx, value=value)

            # Progress log mỗi 1000 rows
            if row_idx % 1000 == 0:
                self.log(f"📝 Đã ghi {row_idx-1}/{self.total_products} rows...")


class HybridExcelWriter:
    """
    Hybrid Excel Writer - Kết hợp Memory + Smart Batching
    Tự động chọn strategy dựa trên số lượng sản phẩm và RAM available
    """

    def __init__(self, excel_path, log_callback=None):
        """
        Khởi tạo Hybrid Excel Writer

        Args:
            excel_path: Đường dẫn file Excel đầu ra
            log_callback: Callback để ghi log
        """
        self.excel_path = excel_path
        self.log_callback = log_callback

        # Thống kê hệ thống
        import psutil
        self.available_memory_gb = psutil.virtual_memory().available / (1024**3)

        # Strategy selection
        self.memory_threshold = 10000  # Nếu < 10k products → dùng Memory mode
        self.use_memory_mode = True  # Default memory mode

        # Memory storage
        self.all_products = []
        self.total_products = 0

        # Batch mode fallback
        self.workbook = None
        self.worksheet = None
        self.current_row = 1
        self.headers_written = False
        self.batch_size = 1000  # Lớn hơn DirectExcelWriter

        # Thread-safe lock
        self.lock = threading.Lock()

        self.log(f"🔥 Khởi tạo Hybrid Excel Writer (RAM: {self.available_memory_gb:.1f}GB)")

    def log(self, message):
        """Helper function để ghi log"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def append_products(self, products):
        """
        Smart append - tự động chọn strategy
        """
        if not products:
            return

        with self.lock:
            if self.use_memory_mode:
                # Memory mode - siêu nhanh
                self.all_products.extend(products)
                self.total_products += len(products)

                # Check if need to switch to batch mode
                if self.total_products > self.memory_threshold:
                    self._switch_to_batch_mode()
                else:
                    self.log(f"🧠 RAM: +{len(products)} (tổng: {self.total_products})")
            else:
                # Batch mode
                self._append_batch_mode(products)

    def _switch_to_batch_mode(self):
        """Chuyển từ memory mode sang batch mode"""
        self.log(f"🔄 Chuyển sang Batch mode (>{self.memory_threshold} products)")
        self.use_memory_mode = False

        # Khởi tạo Excel workbook
        self.workbook = Workbook()
        self.worksheet = self.workbook.active

        # Ghi headers
        self._write_headers_batch()

        # Ghi tất cả data đã có trong memory
        self._flush_memory_to_batch()

    def _write_headers_batch(self):
        """Ghi headers cho batch mode"""
        headers = ['', ' ', '  ']  # 3 cột đầu trống
        headers.extend([
            'Link sản phẩm', 'Link Shop', 'Tên sản phẩm', 'Thương hiệu', 'Mô tả',
            'Ngày tạo', 'Mã Shop', 'Mã Sản phẩm', 'Chuyên mục', 'Chuyên mục.1',
            'Giá hiện tại', 'Giá thấp nhất', 'Giá cao nhất', 'Giảm giá', 'Tồn kho', 'Cân nặng',
            'Hình ảnh', 'Số Đánh giá', 'Số lượt xem', 'Số thích', 'Điểm đánh giá',
            'Đã bán 30 ngày', 'Doanh số 30 ngày', 'Đã bán toàn thời gian', 'Doanh số toàn thời gian',
            'Vị trí', 'Video'
        ])

        for col_idx, header in enumerate(headers, 1):
            self.worksheet.cell(row=1, column=col_idx, value=header)
            self.worksheet.cell(row=1, column=col_idx).font = Font(bold=False)

        self.current_row = 2
        self.headers_written = True

    def _flush_memory_to_batch(self):
        """Ghi data từ memory vào batch mode"""
        if not self.all_products:
            return

        self.log(f"📝 Ghi {len(self.all_products)} products từ memory vào Excel...")

        field_order = ['linkProduct', 'linkShop', 'name', 'brand', 'description',
                      'timeCreate', 'itemID', 'shopID', 'categoryMain', 'categoryTree',
                      'price', 'priceMin', 'priceMax', 'discount', 'stock', 'weight',
                      'image', 'cmtCount', 'viewCount', 'likedCount', 'itemRating',
                      'sold_30day', 'sale_30day', 'sold_alltime', 'sale_alltime',
                      'location', 'video']

        for product in self.all_products:
            # 3 cột đầu
            key_value = f"{product.get('id', '')}__{product.get('shopID', '')}"
            self.worksheet.cell(row=self.current_row, column=1, value=key_value)
            self.worksheet.cell(row=self.current_row, column=2, value=product.get('id', ''))
            self.worksheet.cell(row=self.current_row, column=3, value=product.get('id', ''))

            # Các cột còn lại
            for col_offset, field_name in enumerate(field_order):
                col_idx = 4 + col_offset
                value = product.get(field_name, '')
                if isinstance(value, str):
                    value = clean_text_for_excel(value)
                self.worksheet.cell(row=self.current_row, column=col_idx, value=value)

            self.current_row += 1

        # Clear memory
        self.all_products = []

    def _append_batch_mode(self, products):
        """Append trong batch mode"""
        field_order = ['linkProduct', 'linkShop', 'name', 'brand', 'description',
                      'timeCreate', 'itemID', 'shopID', 'categoryMain', 'categoryTree',
                      'price', 'priceMin', 'priceMax', 'discount', 'stock', 'weight',
                      'image', 'cmtCount', 'viewCount', 'likedCount', 'itemRating',
                      'sold_30day', 'sale_30day', 'sold_alltime', 'sale_alltime',
                      'location', 'video']

        for product in products:
            # 3 cột đầu
            key_value = f"{product.get('id', '')}__{product.get('shopID', '')}"
            self.worksheet.cell(row=self.current_row, column=1, value=key_value)
            self.worksheet.cell(row=self.current_row, column=2, value=product.get('id', ''))
            self.worksheet.cell(row=self.current_row, column=3, value=product.get('id', ''))

            # Các cột còn lại
            for col_offset, field_name in enumerate(field_order):
                col_idx = 4 + col_offset
                value = product.get(field_name, '')
                if isinstance(value, str):
                    value = clean_text_for_excel(value)
                self.worksheet.cell(row=self.current_row, column=col_idx, value=value)

            self.current_row += 1

        self.total_products += len(products)
        self.log(f"📊 BATCH: +{len(products)} (tổng: {self.total_products})")

    def finalize(self):
        """
        Hoàn tất quá trình ghi

        Returns:
            bool: True nếu thành công
        """
        try:
            if self.use_memory_mode:
                # Memory mode - ghi tất cả cùng lúc
                return self._finalize_memory_mode()
            else:
                # Batch mode - chỉ cần save
                return self._finalize_batch_mode()

        except Exception as e:
            self.log(f"❌ Lỗi trong finalize: {str(e)}")
            return False

    def _finalize_memory_mode(self):
        """Finalize memory mode"""
        if not self.all_products:
            self.log("⚠️ Không có dữ liệu để ghi")
            return False

        self.log(f"🚀 Memory mode: Ghi {self.total_products} sản phẩm vào Excel...")

        # Tạo workbook
        wb = Workbook()
        ws = wb.active

        # Ghi headers
        headers = ['', ' ', '  ']  # 3 cột đầu trống
        headers.extend([
            'Link sản phẩm', 'Link Shop', 'Tên sản phẩm', 'Thương hiệu', 'Mô tả',
            'Ngày tạo', 'Mã Shop', 'Mã Sản phẩm', 'Chuyên mục', 'Chuyên mục.1',
            'Giá hiện tại', 'Giá thấp nhất', 'Giá cao nhất', 'Giảm giá', 'Tồn kho', 'Cân nặng',
            'Hình ảnh', 'Số Đánh giá', 'Số lượt xem', 'Số thích', 'Điểm đánh giá',
            'Đã bán 30 ngày', 'Doanh số 30 ngày', 'Đã bán toàn thời gian', 'Doanh số toàn thời gian',
            'Vị trí', 'Video'
        ])

        for col_idx, header in enumerate(headers, 1):
            ws.cell(row=1, column=col_idx, value=header)
            ws.cell(row=1, column=col_idx).font = Font(bold=False)

        # Ghi tất cả data
        field_order = ['linkProduct', 'linkShop', 'name', 'brand', 'description',
                      'timeCreate', 'itemID', 'shopID', 'categoryMain', 'categoryTree',
                      'price', 'priceMin', 'priceMax', 'discount', 'stock', 'weight',
                      'image', 'cmtCount', 'viewCount', 'likedCount', 'itemRating',
                      'sold_30day', 'sale_30day', 'sold_alltime', 'sale_alltime',
                      'location', 'video']

        for row_idx, product in enumerate(self.all_products, 2):
            # 3 cột đầu
            key_value = f"{product.get('id', '')}__{product.get('shopID', '')}"
            ws.cell(row=row_idx, column=1, value=key_value)
            ws.cell(row=row_idx, column=2, value=product.get('id', ''))
            ws.cell(row=row_idx, column=3, value=product.get('id', ''))

            # Các cột còn lại
            for col_offset, field_name in enumerate(field_order):
                col_idx = 4 + col_offset
                value = product.get(field_name, '')
                if isinstance(value, str):
                    value = clean_text_for_excel(value)
                ws.cell(row=row_idx, column=col_idx, value=value)

        # Lưu file
        self.log("💾 Đang lưu file Excel...")
        wb.save(self.excel_path)
        self.log(f"✅ HOÀN TẤT! Đã lưu {self.total_products} sản phẩm vào {self.excel_path}")
        return True

    def _finalize_batch_mode(self):
        """Finalize batch mode"""
        self.log("💾 Đang lưu file Excel (batch mode)...")
        self.workbook.save(self.excel_path)
        self.log(f"✅ HOÀN TẤT! Đã lưu {self.total_products} sản phẩm vào {self.excel_path}")
        return True


class SQLiteBufferExcelWriter:
    """
    SQLite Buffer Excel Writer - Tối ưu tốc độ cho 8 threads với SQLite buffer

    Kiến trúc:
    1. 8 threads scraping → SQLite buffer (siêu nhanh)
    2. Background thread → Export SQLite → Excel (không block scraping)
    3. Zero data loss với ACID compliance
    4. Memory-efficient cho 160k+ products
    """

    def __init__(self, excel_path, log_callback=None):
        """
        Khởi tạo SQLite Buffer Excel Writer

        Args:
            excel_path: Đường dẫn file Excel đầu ra
            log_callback: Callback để ghi log
        """
        self.excel_path = excel_path
        self.log_callback = log_callback

        # Tạo SQLite database tạm thời
        self.temp_db_path = self._create_temp_db_path()
        self.db_connection = None
        self.db_lock = threading.Lock()

        # Tracking
        self.total_products = 0
        self.headers_written = False

        # Buffer cho batch insert
        self.insert_buffer = []
        self.buffer_size = 1000  # 1000 products per batch insert

        # Background export thread
        self.export_thread = None
        self.export_completed = threading.Event()

        self._initialize_database()
        self.log("🗄️ Khởi tạo SQLite Buffer Excel Writer (tối ưu 8 threads)")

    def _create_temp_db_path(self):
        """Tạo đường dẫn SQLite database tạm thời"""
        temp_dir = tempfile.gettempdir()
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        return os.path.join(temp_dir, f"autoshopee_buffer_{timestamp}.db")

    def _initialize_database(self):
        """Khởi tạo SQLite database với schema tối ưu"""
        try:
            self.db_connection = sqlite3.connect(self.temp_db_path, check_same_thread=False)

            # Tối ưu SQLite cho write performance
            cursor = self.db_connection.cursor()
            cursor.execute("PRAGMA journal_mode = WAL")  # Write-Ahead Logging
            cursor.execute("PRAGMA synchronous = NORMAL")  # Balance safety/speed
            cursor.execute("PRAGMA cache_size = 10000")  # 10MB cache
            cursor.execute("PRAGMA temp_store = MEMORY")  # Temp tables in memory

            # Tạo bảng products với schema tối ưu
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id TEXT,
                    shop_id TEXT,
                    link_product TEXT,
                    link_shop TEXT,
                    name TEXT,
                    brand TEXT,
                    description TEXT,
                    time_create TEXT,
                    item_id TEXT,
                    category_main TEXT,
                    category_tree TEXT,
                    price TEXT,
                    price_min TEXT,
                    price_max TEXT,
                    discount TEXT,
                    stock TEXT,
                    weight TEXT,
                    image TEXT,
                    cmt_count TEXT,
                    view_count TEXT,
                    liked_count TEXT,
                    item_rating TEXT,
                    sold_30day TEXT,
                    sale_30day TEXT,
                    raw_data TEXT
                )
            """)

            # Index cho performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_shop_id ON products(shop_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_product_id ON products(product_id)")

            self.db_connection.commit()
            self.log("✅ SQLite database khởi tạo thành công")

        except Exception as e:
            self.log(f"❌ Lỗi khởi tạo SQLite database: {str(e)}")
            raise

    def log(self, message):
        """Helper function để ghi log"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def append_products(self, products):
        """
        Thêm danh sách sản phẩm vào SQLite buffer (thread-safe, siêu nhanh)

        Args:
            products: List các sản phẩm từ API
        """
        if not products:
            return

        with self.db_lock:
            try:
                # Thêm products vào buffer
                for product in products:
                    self.insert_buffer.append(self._prepare_product_data(product))

                # Batch insert khi buffer đủ lớn
                if len(self.insert_buffer) >= self.buffer_size:
                    self._flush_buffer_to_sqlite()

            except Exception as e:
                self.log(f"❌ Lỗi khi ghi SQLite buffer: {str(e)}")

    def _prepare_product_data(self, product):
        """Chuẩn bị data cho SQLite insert"""
        return (
            product.get('id', ''),
            product.get('shopID', ''),
            product.get('linkProduct', ''),
            product.get('linkShop', ''),
            product.get('name', ''),
            product.get('brand', ''),
            product.get('description', ''),
            product.get('timeCreate', ''),
            product.get('itemID', ''),
            product.get('categoryMain', ''),
            product.get('categoryTree', ''),
            product.get('price', ''),
            product.get('priceMin', ''),
            product.get('priceMax', ''),
            product.get('discount', ''),
            product.get('stock', ''),
            product.get('weight', ''),
            product.get('image', ''),
            product.get('cmtCount', ''),
            product.get('viewCount', ''),
            product.get('likedCount', ''),
            product.get('itemRating', ''),
            product.get('sold_30day', ''),
            product.get('sale_30day', ''),
            json.dumps(product)  # Raw data backup
        )

    def _flush_buffer_to_sqlite(self):
        """Ghi buffer vào SQLite (batch insert siêu nhanh)"""
        if not self.insert_buffer:
            return

        try:
            cursor = self.db_connection.cursor()

            # Batch insert với executemany (siêu nhanh)
            cursor.executemany("""
                INSERT INTO products (
                    product_id, shop_id, link_product, link_shop, name, brand,
                    description, time_create, item_id, category_main, category_tree,
                    price, price_min, price_max, discount, stock, weight, image,
                    cmt_count, view_count, liked_count, item_rating, sold_30day,
                    sale_30day, raw_data
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, self.insert_buffer)

            self.db_connection.commit()

            # Update tracking
            batch_size = len(self.insert_buffer)
            self.total_products += batch_size
            self.log(f"🗄️ SQLite BATCH: Đã ghi {batch_size} sản phẩm (tổng: {self.total_products})")

            # Clear buffer
            self.insert_buffer = []

        except Exception as e:
            self.log(f"❌ Lỗi SQLite batch insert: {str(e)}")

    def finalize(self):
        """Hoàn tất quá trình ghi và export sang Excel"""
        try:
            # Flush buffer cuối cùng
            with self.db_lock:
                if self.insert_buffer:
                    self._flush_buffer_to_sqlite()

            self.log(f"🗄️ SQLite hoàn tất: {self.total_products} sản phẩm")

            # Bắt đầu export sang Excel trong background
            self.export_thread = threading.Thread(target=self._export_to_excel)
            self.export_thread.start()

            # Đợi export hoàn tất
            self.export_thread.join()

            # Cleanup
            self._cleanup()

            return True

        except Exception as e:
            self.log(f"❌ Lỗi finalize SQLite writer: {str(e)}")
            return False

    def _export_to_excel(self):
        """Export dữ liệu từ SQLite sang Excel (background thread)"""
        try:
            self.log("📊 Bắt đầu export SQLite → Excel...")

            # Đọc dữ liệu từ SQLite
            cursor = self.db_connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM products")
            total_count = cursor.fetchone()[0]

            self.log(f"📊 Đang export {total_count} sản phẩm từ SQLite...")

            # Tạo Excel workbook
            wb = Workbook()
            ws = wb.active

            # Ghi headers
            self._write_excel_headers(ws)

            # Export data theo batch để tiết kiệm memory
            batch_size = 5000
            offset = 0
            current_row = 2  # Bắt đầu từ row 2 (row 1 là headers)

            while offset < total_count:
                cursor.execute("""
                    SELECT * FROM products
                    ORDER BY id
                    LIMIT ? OFFSET ?
                """, (batch_size, offset))

                batch_data = cursor.fetchall()
                if not batch_data:
                    break

                # Ghi batch vào Excel
                for row_data in batch_data:
                    excel_row = self._convert_sqlite_row_to_excel(row_data)
                    for col_idx, value in enumerate(excel_row, 1):
                        ws.cell(row=current_row, column=col_idx, value=value)
                    current_row += 1

                offset += batch_size
                self.log(f"📊 Export progress: {min(offset, total_count)}/{total_count}")

            # Lưu Excel file
            wb.save(self.excel_path)
            self.log(f"✅ Export hoàn tất: {self.excel_path}")

        except Exception as e:
            self.log(f"❌ Lỗi export SQLite → Excel: {str(e)}")
        finally:
            self.export_completed.set()

    def _write_excel_headers(self, worksheet):
        """Ghi headers vào Excel worksheet"""
        # Column mapping giống DirectExcelWriter
        headers = ['', ' ', '  ']  # 3 cột đầu trống
        headers.extend([
            'Link sản phẩm', 'Link Shop', 'Tên sản phẩm', 'Thương hiệu', 'Mô tả',
            'Ngày tạo', 'Mã Shop', 'Mã Sản phẩm', 'Chuyên mục', 'Chuyên mục.1',
            'Giá hiện tại', 'Giá thấp nhất', 'Giá cao nhất', 'Giảm giá', 'Tồn kho',
            'Cân nặng', 'Hình ảnh', 'Số Đánh giá', 'Số lượt xem', 'Số thích',
            'Điểm đánh giá', 'Đã bán 30 ngày', 'Doanh số 30 ngày'
        ])

        for col_idx, header in enumerate(headers, 1):
            worksheet.cell(row=1, column=col_idx, value=header)

    def _convert_sqlite_row_to_excel(self, sqlite_row):
        """Convert SQLite row sang Excel row format"""
        # sqlite_row format: (id, product_id, shop_id, link_product, ...)
        product_id = sqlite_row[1]
        shop_id = sqlite_row[2]

        # 3 cột đầu tiên
        key_value = f"{product_id}__{shop_id}"
        excel_row = [key_value, product_id, product_id]

        # Các cột còn lại (bỏ qua id và raw_data)
        excel_row.extend(sqlite_row[3:-1])  # Từ link_product đến sale_30day

        return excel_row

    def _cleanup(self):
        """Cleanup SQLite database và temp files"""
        try:
            if self.db_connection:
                self.db_connection.close()

            if os.path.exists(self.temp_db_path):
                os.remove(self.temp_db_path)
                self.log("🗑️ Đã cleanup SQLite temp files")

        except Exception as e:
            self.log(f"⚠️ Lỗi cleanup: {str(e)}")


class DirectExcelWriter:
    """
    Direct Excel Writer - Ghi trực tiếp Excel để tối ưu tốc độ
    Loại bỏ bước CSV → Excel conversion
    """

    # Column mapping giống StreamingExcelWriter
    COLUMN_MAPPING = {
        'key_column': '',
        'sku_column': ' ',
        'id_column': '  ',
        'linkProduct': 'Link sản phẩm',
        'linkShop': 'Link Shop',
        'name': 'Tên sản phẩm',
        'brand': 'Thương hiệu',
        'description': 'Mô tả',
        'timeCreate': 'Ngày tạo',
        'itemID': 'Mã Shop',
        'shopID': 'Mã Sản phẩm',
        'categoryMain': 'Chuyên mục',
        'categoryTree': 'Chuyên mục.1',
        'price': 'Giá hiện tại',
        'priceMin': 'Giá thấp nhất',
        'priceMax': 'Giá cao nhất',
        'discount': 'Giảm giá',
        'stock': 'Tồn kho',
        'weight': 'Cân nặng',
        'image': 'Hình ảnh',
        'cmtCount': 'Số Đánh giá',
        'viewCount': 'Số lượt xem',
        'likedCount': 'Số thích',
        'itemRating': 'Điểm đánh giá',
        'sold_30day': 'Đã bán 30 ngày',
        'sale_30day': 'Doanh số 30 ngày',
        'sold_alltime': 'Đã bán toàn thời gian',
        'sale_alltime': 'Doanh số toàn thời gian',
        'location': 'Vị trí',
        'video': 'Video'
    }

    def __init__(self, excel_path, log_callback=None):
        """
        Khởi tạo Direct Excel Writer

        Args:
            excel_path: Đường dẫn file Excel đầu ra
            log_callback: Callback để ghi log
        """
        self.excel_path = excel_path
        self.log_callback = log_callback

        # Khởi tạo Excel workbook
        self.workbook = Workbook()
        self.worksheet = self.workbook.active

        # Thread-safe lock
        self.excel_lock = threading.Lock()

        # Tracking
        self.current_row = 1
        self.headers_written = False
        self.total_products = 0

        # LARGE DATASET BUFFER - tối ưu cho 500-999 shop
        self.write_buffer = []
        self.buffer_size = 2000  # 2000 products per batch (tối ưu cho large dataset)

        self.log("📁 Khởi tạo Direct Excel Writer (tối ưu tốc độ)")

    def log(self, message):
        """Helper function để ghi log"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def append_products(self, products):
        """
        Thêm danh sách sản phẩm vào Excel (thread-safe)

        Args:
            products: List các sản phẩm từ API
        """
        if not products:
            return

        with self.excel_lock:
            try:
                # Ghi headers nếu chưa ghi (chỉ 1 lần)
                if not self.headers_written:
                    self._write_excel_headers()
                    self.headers_written = True

                # Thêm products vào buffer
                self.write_buffer.extend(products)

                # Batch write khi buffer đủ lớn
                if len(self.write_buffer) >= self.buffer_size:
                    self._flush_buffer_to_excel()

            except Exception as e:
                self.log(f"❌ Lỗi khi ghi Excel: {str(e)}")

    def _write_excel_headers(self):
        """Ghi headers vào Excel"""
        # Ghi headers theo COLUMN_MAPPING
        col_idx = 1

        # 3 cột đầu trống
        for empty_header in ['', ' ', '  ']:
            self.worksheet.cell(row=1, column=col_idx, value='')
            self.worksheet.cell(row=1, column=col_idx).font = Font(bold=False)
            col_idx += 1

        # Các cột còn lại
        for field_name, excel_header in self.COLUMN_MAPPING.items():
            if field_name not in ['key_column', 'sku_column', 'id_column']:
                self.worksheet.cell(row=1, column=col_idx, value=excel_header)
                self.worksheet.cell(row=1, column=col_idx).font = Font(bold=False)
                col_idx += 1

        self.current_row = 2  # Dữ liệu bắt đầu từ row 2

    def _flush_buffer_to_excel(self):
        """Ghi buffer vào Excel và clear buffer - BATCH OPTIMIZED"""
        if not self.write_buffer:
            return

        # BATCH WRITE: Chuẩn bị tất cả data trước khi ghi
        batch_data = []
        for product in self.write_buffer:
            row_data = self._prepare_product_row_data(product)
            batch_data.append(row_data)

        # BULK INSERT: Ghi tất cả rows cùng lúc
        start_row = self.current_row
        for i, row_data in enumerate(batch_data):
            current_row_num = start_row + i
            for col_idx, value in enumerate(row_data, 1):
                self.worksheet.cell(row=current_row_num, column=col_idx, value=value)

        # Update tracking
        self.current_row += len(batch_data)
        self.total_products += len(self.write_buffer)
        self.log(f"📊 BATCH: Đã ghi {len(self.write_buffer)} sản phẩm vào Excel (tổng: {self.total_products})")

        # Clear buffer
        self.write_buffer = []

    def _prepare_product_row_data(self, product):
        """Chuẩn bị data cho một row sản phẩm (không ghi trực tiếp) - OPTIMIZED"""
        row_data = []

        # 3 cột đầu tiên
        key_value = f"{product.get('id', '')}__{product.get('shopID', '')}"
        row_data.append(key_value)
        row_data.append(product.get('id', ''))
        row_data.append(product.get('id', ''))

        # Các cột còn lại - PRE-ORDERED để tránh loop qua dict
        for field_name in ['linkProduct', 'linkShop', 'name', 'brand', 'description',
                          'timeCreate', 'itemID', 'shopID', 'categoryMain', 'categoryTree',
                          'price', 'priceMin', 'priceMax', 'discount', 'stock', 'weight',
                          'image', 'cmtCount', 'viewCount', 'likedCount', 'itemRating',
                          'sold_30day', 'sale_30day', 'sold_alltime', 'sale_alltime',
                          'location', 'video']:
            value = product.get(field_name, '')
            if isinstance(value, str):
                value = clean_text_for_excel(value)
            row_data.append(value)

        return row_data

    def _write_product_row(self, product):
        """Ghi một row sản phẩm vào Excel - DEPRECATED, dùng _prepare_product_row_data"""
        # Giữ lại để backward compatibility
        row_data = self._prepare_product_row_data(product)
        for col_idx, value in enumerate(row_data, 1):
            self.worksheet.cell(row=self.current_row, column=col_idx, value=value)
        self.current_row += 1

    def finalize(self):
        """
        Hoàn tất quá trình: ghi buffer cuối cùng và lưu file

        Returns:
            bool: True nếu thành công
        """
        try:
            with self.excel_lock:
                # Ghi buffer cuối cùng
                if self.write_buffer:
                    self._flush_buffer_to_excel()

                # Lưu file Excel
                self.log("💾 Đang lưu file Excel...")
                self.workbook.save(self.excel_path)
                self.log(f"✅ Hoàn tất! Đã lưu {self.total_products} sản phẩm vào {self.excel_path}")

                return True

        except Exception as e:
            self.log(f"❌ Lỗi trong finalize: {str(e)}")
            return False


class CSVThenExcelWriter:
    """
    Writer class để ghi CSV trước, sau đó convert sang Excel
    Tối ưu tốc độ ghi dữ liệu và fault tolerance
    """

    # Column mapping configuration - không hard code
    COLUMN_MAPPING = {
        # 3 cột đầu tiên có header trống
        'key_column': '',
        'sku_column': ' ',  # Space để phân biệt
        'id_column': '  ',  # 2 spaces để phân biệt

        # Mapping các cột dữ liệu
        'linkProduct': 'Link sản phẩm',
        'linkShop': 'Link Shop',
        'name': 'Tên sản phẩm',
        'brand': 'Thương hiệu',
        'description': 'Mô tả',
        'timeCreate': 'Ngày tạo',
        'itemID': 'Mã Shop',
        'shopID': 'Mã Sản phẩm',
        'categoryMain': 'Chuyên mục',
        'categoryTree': 'Chuyên mục.1',
        'price': 'Giá hiện tại',
        'priceMin': 'Giá thấp nhất',
        'priceMax': 'Giá cao nhất',
        'discount': 'Giảm giá',
        'stock': 'Tồn kho',
        'weight': 'Cân nặng',
        'image': 'Hình ảnh',
        'cmtCount': 'Số Đánh giá',
        'viewCount': 'Số lượt xem',
        'likedCount': 'Số thích',
        'itemRating': 'Điểm đánh giá',
        'sold_30day': 'Đã bán 30 ngày',
        'sale_30day': 'Doanh số 30 ngày',
        'sold_alltime': 'Đã bán toàn thời gian',
        'sale_alltime': 'Doanh số toàn thời gian',
        'location': 'Vị trí',
        'video': 'Video'
    }

    def __init__(self, final_excel_path, log_callback=None):
        """
        Initialize CSV then Excel writer

        Args:
            final_excel_path: Đường dẫn file Excel cuối cùng
            log_callback: Callback function để ghi log
        """
        self.final_excel_path = final_excel_path
        self.log_callback = log_callback

        # Tạo tên file CSV tạm thời
        self.csv_temp_path = self._create_temp_csv_path(final_excel_path)

        # Initialize CSV writer
        self.csv_file = None
        self.csv_writer = None
        self.headers_written = False
        self.total_rows = 0

        # Thread-safe lock cho CSV writing
        self.csv_lock = threading.Lock()

        # Buffer tối ưu cho 8 threads
        self.write_buffer_count = 0
        self.flush_every_n_writes = 10  # Flush mỗi 10 lần ghi (tối ưu cho 8 threads)
        self.batch_size_threshold = 200  # Flush khi có >= 200 products trong batch

        # Mở CSV file để ghi
        self._initialize_csv_writer()

    def _create_temp_csv_path(self, excel_path):
        """Tạo đường dẫn file CSV tạm thời"""
        base_name = os.path.splitext(excel_path)[0]
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{base_name}_temp_{timestamp}.csv"

    def _initialize_csv_writer(self):
        """Khởi tạo CSV writer với buffer tối ưu cho 8 threads"""
        try:
            # Mở file với buffer size lớn hơn để tối ưu cho 8 threads
            self.csv_file = open(
                self.csv_temp_path,
                'w',
                newline='',
                encoding='utf-8',
                buffering=65536  # 64KB buffer cho performance tốt hơn với 8 threads
            )
            self.csv_writer = csv.writer(self.csv_file)
            self.log(f"📁 Khởi tạo CSV temp file (8-thread optimized): {os.path.basename(self.csv_temp_path)}")
        except Exception as e:
            self.log(f"❌ Lỗi khởi tạo CSV writer: {str(e)}")
            raise

    def log(self, message):
        """Helper function để ghi log"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def append_products(self, products):
        """
        Thêm danh sách sản phẩm vào CSV (thread-safe, tối ưu tốc độ)

        Args:
            products: List các sản phẩm từ API
        """
        if not products:
            return

        # Thread-safe CSV writing với micro-delay để giảm contention
        with self.csv_lock:
            try:
                # Micro delay để giảm lock contention với 8 threads
                time.sleep(0.001)  # 1ms delay
                # Ghi headers nếu chưa ghi (chỉ 1 lần)
                if not self.headers_written:
                    self._write_csv_headers_fast(products[0])
                    self.headers_written = True

                # Batch write tất cả products cùng lúc (tối ưu I/O)
                rows_data = []
                for product in products:
                    row_data = self._format_product_row_fast(product)
                    rows_data.append(row_data)

                # Write all rows at once
                self.csv_writer.writerows(rows_data)
                self.total_rows += len(products)
                self.write_buffer_count += 1

                # Smart flush logic tối ưu cho 8 threads
                should_flush = (
                    self.write_buffer_count >= self.flush_every_n_writes or  # Flush theo count
                    len(products) >= self.batch_size_threshold  # Flush với batch lớn
                )

                if should_flush:
                    self.csv_file.flush()
                    self.write_buffer_count = 0

                # Không log ở đây để tránh spam - để main function log

            except Exception as e:
                self.log(f"❌ Lỗi khi ghi CSV: {str(e)}")

    def _clean_products_data(self, products):
        """Làm sạch dữ liệu sản phẩm"""
        clean_products = []
        for product in products:
            clean_product = {}
            for key, value in product.items():
                if isinstance(value, str):
                    clean_product[key] = clean_text_for_excel(value)
                else:
                    clean_product[key] = value
            clean_products.append(clean_product)
        return clean_products

    def _write_csv_headers_fast(self, sample_product):
        """Ghi headers vào CSV tối ưu tốc độ"""
        # Pre-defined headers order để tránh loop
        headers = ['id', 'shopID', 'id']  # 3 cột đầu tiên

        # Thêm các cột theo thứ tự cố định (không loop qua dict)
        headers.extend([
            'linkProduct', 'linkShop', 'name', 'brand', 'description',
            'timeCreate', 'itemID', 'shopID', 'categoryMain', 'categoryTree',
            'price', 'priceMin', 'priceMax', 'discount', 'stock', 'weight',
            'image', 'cmtCount', 'viewCount', 'likedCount', 'itemRating',
            'sold_30day', 'sale_30day', 'sold_alltime', 'sale_alltime',
            'location', 'video'
        ])

        self.csv_writer.writerow(headers)

    def _format_product_row_fast(self, product):
        """Format một row sản phẩm tối ưu tốc độ (không clean text)"""
        # Tạo key column
        key_value = f"{product.get('id', '')}__{product.get('shopID', '')}"

        # Pre-build row với thứ tự cố định (tránh loop)
        return [
            key_value,
            product.get('id', ''),
            product.get('id', ''),
            product.get('linkProduct', ''),
            product.get('linkShop', ''),
            product.get('name', ''),
            product.get('brand', ''),
            product.get('description', ''),
            product.get('timeCreate', ''),
            product.get('itemID', ''),
            product.get('shopID', ''),
            product.get('categoryMain', ''),
            product.get('categoryTree', ''),
            product.get('price', ''),
            product.get('priceMin', ''),
            product.get('priceMax', ''),
            product.get('discount', ''),
            product.get('stock', ''),
            product.get('weight', ''),
            product.get('image', ''),
            product.get('cmtCount', ''),
            product.get('viewCount', ''),
            product.get('likedCount', ''),
            product.get('itemRating', ''),
            product.get('sold_30day', ''),
            product.get('sale_30day', ''),
            product.get('sold_alltime', ''),
            product.get('sale_alltime', ''),
            product.get('location', ''),
            product.get('video', '')
        ]

    def finalize(self):
        """
        Hoàn tất quá trình: đóng CSV và convert sang Excel

        Returns:
            bool: True nếu thành công
        """
        try:
            # Đóng CSV file với final flush
            if self.csv_file:
                # Final flush để đảm bảo tất cả data được ghi
                self.csv_file.flush()
                self.csv_file.close()
                self.log(f"📊 Đã ghi {self.total_rows} rows vào CSV")

            # Convert CSV sang Excel với smart retry
            self.log("🔄 Bắt đầu convert CSV → Excel...")
            success = self._convert_csv_to_excel_with_retry()

            if success:
                self.log(f"✅ Hoàn tất! File Excel: {self.final_excel_path}")
                # Xóa file CSV tạm thời
                self._cleanup_temp_files()
                return True
            else:
                self.log(f"❌ Conversion thất bại! CSV backup: {self.csv_temp_path}")
                return False

        except Exception as e:
            self.log(f"❌ Lỗi trong finalize: {str(e)}")
            return False

    def _convert_csv_to_excel_with_retry(self, max_retries=2):
        """Convert CSV sang Excel với 2 methods đáng tin cậy"""
        for attempt in range(max_retries):
            try:
                self.log(f"🔄 Conversion attempt {attempt + 1}/{max_retries}")

                if attempt == 0:
                    # Method 1: Sử dụng format chuẩn (giống StreamingExcelWriter)
                    self.log("📊 Thử Method 1: Standard format (giống StreamingExcelWriter)")
                    success = self._convert_using_standard_format()
                else:
                    # Method 2: Pandas simple conversion (fallback đáng tin cậy)
                    self.log("📊 Thử Method 2: Pandas simple conversion")
                    success = self._convert_using_pandas_simple()

                if success:
                    self.log(f"✅ Conversion thành công với Method {attempt + 1}")
                    return True

            except Exception as e:
                self.log(f"❌ Method {attempt + 1} failed: {str(e)}")

        return False

    def _convert_using_standard_format(self):
        """Method 1: Convert với format chuẩn giống StreamingExcelWriter"""
        try:
            # Đọc CSV
            df = pd.read_csv(self.csv_temp_path)

            # Format DataFrame giống hệt StreamingExcelWriter
            formatted_df = self._format_dataframe_like_streaming_writer(df)

            # Tạo Excel với format chuẩn
            wb = Workbook()
            ws = wb.active

            # Ghi headers (3 cột đầu trống)
            for c_idx, column in enumerate(formatted_df.columns, 1):
                if c_idx <= 3:
                    header_value = ""  # 3 cột đầu trống
                else:
                    header_value = column
                ws.cell(row=1, column=c_idx, value=header_value)
                ws.cell(row=1, column=c_idx).font = Font(bold=False)

            # Ghi dữ liệu từ row 2
            for r_idx, (_, row) in enumerate(formatted_df.iterrows(), 2):
                for c_idx, value in enumerate(row, 1):
                    try:
                        if isinstance(value, str):
                            value = clean_text_for_excel(value)
                        ws.cell(row=r_idx, column=c_idx, value=value)
                    except:
                        ws.cell(row=r_idx, column=c_idx, value="[Lỗi dữ liệu]")

            wb.save(self.final_excel_path)
            return True

        except Exception:
            return False

    def _format_dataframe_like_streaming_writer(self, df):
        """Format DataFrame giống hệt StreamingExcelWriter._format_dataframe()"""
        # Tạo dữ liệu cho 3 cột đầu tiên từ CSV
        key_col = df.iloc[:, 0]  # Cột đầu tiên là key
        sku_col = df.iloc[:, 1]  # Cột thứ 2 là sku
        id_col = df.iloc[:, 2]   # Cột thứ 3 là id

        # Tạo DataFrame mới với cấu trúc chuẩn
        new_df = pd.DataFrame({
            '': key_col,    # Cột trống 1
            ' ': sku_col,   # Cột trống 2
            '  ': id_col    # Cột trống 3
        })

        # Thêm các cột khác theo COLUMN_MAPPING
        for field_name, excel_header in self.COLUMN_MAPPING.items():
            if field_name not in ['key_column', 'sku_column', 'id_column']:
                if field_name in df.columns:
                    new_df[excel_header] = df[field_name]
                else:
                    new_df[excel_header] = ''

        return new_df

    def _convert_using_pandas_simple(self):
        """Method 2: Fallback conversion với pandas (đáng tin cậy)"""
        try:
            df = pd.read_csv(self.csv_temp_path)
            df.to_excel(self.final_excel_path, index=False)
            return True
        except Exception:
            return False

    def _cleanup_temp_files(self):
        """Xóa các file tạm thời"""
        try:
            if os.path.exists(self.csv_temp_path):
                os.remove(self.csv_temp_path)
                self.log(f"🗑️ Đã xóa CSV temp file")
        except Exception as e:
            self.log(f"⚠️ Không thể xóa temp file: {str(e)}")

def save_to_excel(products, filename="autoshopee_products.xlsx", add_shop_info=True, log_callback=None):
    """
    Lưu danh sách sản phẩm vào file Excel với định dạng cột theo yêu cầu

    Args:
        products (list): Danh sách các sản phẩm từ API
        filename (str, optional): Tên file Excel đầu ra. Mặc định là "autoshopee_products.xlsx"
        add_shop_info (bool, optional): Thêm thông tin shop vào dữ liệu. Mặc định là True
        log_callback (function): Callback function để ghi log
    """
    def log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)

    try:
        # Kiểm tra xem products có phải là list không
        if not isinstance(products, list):
            log("❌ Dữ liệu không phải là danh sách sản phẩm hợp lệ")
            return

        if not products:
            log("❌ Không có sản phẩm nào để lưu")
            return

        # Kiểm tra xem sản phẩm đầu tiên có phải là dict không
        if not isinstance(products[0], dict):
            log("❌ Dữ liệu sản phẩm không đúng định dạng")
            return

        # Kiểm tra các trường quan trọng
        important_fields = ['id', 'name', 'price']
        missing_fields = [field for field in important_fields if field not in products[0]]
        if missing_fields:
            log(f"⚠️ Dữ liệu thiếu các trường quan trọng: {', '.join(missing_fields)}")

        # Làm sạch dữ liệu để tránh lỗi khi ghi vào Excel
        clean_products = []
        for product in products:
            clean_product = {}
            for key, value in product.items():
                if isinstance(value, str):
                    clean_product[key] = clean_text_for_excel(value)
                else:
                    clean_product[key] = value
            clean_products.append(clean_product)

        # Tạo DataFrame từ danh sách sản phẩm đã làm sạch
        df = pd.json_normalize(clean_products)

        # Thêm thông tin shop để phân biệt sản phẩm từ các shop khác nhau
        if add_shop_info and 'shopID' in df.columns:
            # Nhóm sản phẩm theo shopID và đếm số lượng
            shop_counts = df.groupby('shopID').size().reset_index(name='product_count')
            log("\n📊 Thống kê theo shop:")
            for _, row in shop_counts.iterrows():
                shop_id = row['shopID']
                product_count = row['product_count']
                log(f"  - Shop ID {shop_id}: {product_count} sản phẩm")

        # 1. Chuyển đổi các cột sang kiểu số nếu cần
        for col in ['stock', 'sold_30day', 'price', 'priceMin', 'priceMax']:
            if col in df.columns and df[col].dtype == 'object':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # 2. Tạo các cột key và sku nếu chúng không tồn tại
        if 'id' in df.columns:
            # Nếu không có cột key, tạo cột key từ id
            if 'key' not in df.columns:
                df['key'] = df['id'].astype(str) + '_' + df['shopID'].astype(str)  # Thêm shopID làm suffix

            # Nếu không có cột sku, tạo cột sku từ id
            if 'sku' not in df.columns:
                df['sku'] = df['id']

        # Tạo DataFrame mới với cấu trúc cột theo yêu cầu
        # Tạo dữ liệu cho 3 cột đầu tiên
        key_col = df['id'].astype(str) + '_' + df['shopID'].astype(str)
        sku_col = df['id']
        id_col = df['id']

        # Tạo DataFrame với 3 cột đầu tiên có header trống
        new_df = pd.DataFrame({
            'key': key_col,
            'sku': sku_col,
            'id': id_col
        })

        # Đổi tên các cột thành trống
        new_df = new_df.rename(columns={
            'key': '',
            'sku': '',
            'id': ''
        })

        # Thêm các cột còn lại
        new_df['Link sản phẩm'] = df['linkProduct'] if 'linkProduct' in df.columns else ''
        new_df['Link Shop'] = df['linkShop'] if 'linkShop' in df.columns else ''
        new_df['Tên sản phẩm'] = df['name'] if 'name' in df.columns else ''
        new_df['Thương hiệu'] = df['brand'] if 'brand' in df.columns else ''
        new_df['Mô tả'] = df['description'] if 'description' in df.columns else ''
        new_df['Ngày tạo'] = df['timeCreate'] if 'timeCreate' in df.columns else ''
        new_df['Mã Shop'] = df['itemID'] if 'itemID' in df.columns else ''
        new_df['Mã Sản phẩm'] = df['shopID'] if 'shopID' in df.columns else ''
        new_df['Chuyên mục'] = df['categoryMain'] if 'categoryMain' in df.columns else ''
        new_df['Chuyên mục.1'] = df['categoryTree'] if 'categoryTree' in df.columns else ''
        new_df['Giá hiện tại'] = df['price'] if 'price' in df.columns else ''
        new_df['Giá thấp nhất'] = df['priceMin'] if 'priceMin' in df.columns else ''
        new_df['Giá cao nhất'] = df['priceMax'] if 'priceMax' in df.columns else ''
        new_df['Giảm giá'] = df['discount'] if 'discount' in df.columns else ''
        new_df['Tồn kho'] = df['stock'] if 'stock' in df.columns else ''
        new_df['Cân nặng'] = df['weight'] if 'weight' in df.columns else ''
        new_df['Hình ảnh'] = df['image'] if 'image' in df.columns else ''
        new_df['Số Đánh giá'] = df['cmtCount'] if 'cmtCount' in df.columns else ''
        new_df['Số lượt xem'] = df['viewCount'] if 'viewCount' in df.columns else ''
        new_df['Số thích'] = df['likedCount'] if 'likedCount' in df.columns else ''
        new_df['Điểm đánh giá'] = df['itemRating'] if 'itemRating' in df.columns else ''
        new_df['Đã bán 30 ngày'] = df['sold_30day'] if 'sold_30day' in df.columns else ''
        new_df['Doanh số 30 ngày'] = df['sale_30day'] if 'sale_30day' in df.columns else ''
        new_df['Đã bán toàn thời gian'] = df['sold_alltime'] if 'sold_alltime' in df.columns else ''
        new_df['Doanh số toàn thời gian'] = df['sale_alltime'] if 'sale_alltime' in df.columns else ''
        new_df['Vị trí'] = df['location'] if 'location' in df.columns else ''
        new_df['Video'] = df['video'] if 'video' in df.columns else ''

        # Tạo workbook mới
        wb = Workbook()
        ws = wb.active

        # Lỗi thường xảy ra với các ký tự đặc biệt - thêm xử lý an toàn
        try:
            # Thêm dữ liệu từ DataFrame
            rows = dataframe_to_rows(new_df, index=False, header=True)
            for r_idx, row in enumerate(rows, 1):
                for c_idx, value in enumerate(row, 1):
                    try:
                        # Thêm xử lý đặc biệt cho các ký tự không hợp lệ
                        if isinstance(value, str):
                            # Làm sạch giá trị một lần nữa để đảm bảo an toàn
                            value = clean_text_for_excel(value)
                        ws.cell(row=r_idx, column=c_idx, value=value)

                        # Đặt font không in đậm cho header
                        if r_idx == 1:
                            ws.cell(row=r_idx, column=c_idx).font = Font(bold=False)
                    except Exception as cell_error:
                        log(f"⚠️ Lỗi khi thêm giá trị vào ô ({r_idx}, {c_idx}): {str(cell_error)}")
                        # Thay thế bằng giá trị an toàn
                        ws.cell(row=r_idx, column=c_idx, value="[Ký tự không hỗ trợ]")
        except Exception as e:
            log(f"⚠️ Lỗi khi chuyển DataFrame sang Excel: {str(e)}")
            # Thử phương pháp khác để lưu file
            try:
                log("🔄 Thử phương pháp khác để lưu file...")
                # Lưu trực tiếp bằng pandas
                with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                    new_df.to_excel(writer, index=False, sheet_name='Sheet')
                log(f"📁 Đã lưu file Excel: {filename} bằng phương pháp thay thế")
                return True
            except Exception as alt_error:
                log(f"❌ Lỗi khi lưu file bằng phương pháp thay thế: {str(alt_error)}")
                return False

        try:
            # Lưu workbook
            wb.save(filename)
            log(f"📁 Đã lưu file Excel: {filename} (header không in đậm)")
        except Exception as save_error:
            log(f"❌ Lỗi khi lưu file Excel: {str(save_error)}")
            return False

        # In thông tin về số lượng sản phẩm và các trường dữ liệu
        log(f"📊 Thống kê:")
        log(f"  - Số lượng sản phẩm: {len(products)}")
        log(f"  - Số lượng trường dữ liệu: {len(new_df.columns)}")
        log(f"  - Đã sắp xếp các cột theo yêu cầu")

        return True

    except Exception as e:
        log(f"❌ Lỗi không xác định khi lưu file Excel: {str(e)}")
        return False

def fetch_all_products_from_multiple_shops_csv_then_excel(username, password, match_ids, output_filename, headless=True, timeout=30, max_retries=3, log_callback=None, max_workers=8):
    """
    Lấy dữ liệu từ nhiều shop và ghi CSV trước, sau đó convert sang Excel (tối ưu tốc độ)

    Args:
        username: Tên đăng nhập
        password: Mật khẩu
        match_ids: Danh sách ID shop
        output_filename: Tên file Excel đầu ra
        headless: Chạy ở chế độ headless
        timeout: Thời gian timeout
        max_retries: Số lần thử lại tối đa
        log_callback: Callback để ghi log
        max_workers: Số luồng tối đa chạy song song

    Returns:
        bool: True nếu thành công
    """
    def log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)

    try:
        # Đăng nhập trước để lấy cookies và headers
        log("🔑 Đang đăng nhập vào Autoshopee...")
        get_logged_in_cookies(username, password, headless=headless)

        # Lấy headers đã được chuẩn bị
        headers = get_auth_headers()

        if not headers:
            raise Exception("❌ Không thể lấy thông tin xác thực!")

        log("✅ Đăng nhập thành công.")

        # Khởi tạo CSV then Excel writer để ghi tối ưu
        log("📁 Khởi tạo CSV writer...")
        csv_excel_writer = CSVThenExcelWriter(output_filename, log_callback)

        # Tạo và cấu hình session requests để tái sử dụng
        session = requests.Session()
        shop_count = 0
        total_products = 0

        # Sử dụng ThreadPoolExecutor để chạy đa luồng
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Bắt đầu các tác vụ lấy dữ liệu cho từng shop và lưu futures
            futures = {
                executor.submit(
                    fetch_shop_products,
                    match_id,
                    session,
                    headers,
                    timeout,
                    max_retries,
                    log_callback
                ): match_id for match_id in match_ids
            }

            # Thu thập kết quả khi các tác vụ hoàn thành và ghi ngay vào CSV
            for i, future in enumerate(concurrent.futures.as_completed(futures)):
                match_id = futures[future]
                try:
                    shop_products = future.result()
                    if shop_products:
                        # Ghi ngay dữ liệu shop vào CSV (siêu nhanh)
                        csv_excel_writer.append_products(shop_products)
                        total_products += len(shop_products)
                        log(f"📊 Đã ghi {len(shop_products)} sản phẩm từ shop {match_id} vào CSV")

                    shop_count += 1
                    log(f"🔄 Tiến độ: {shop_count}/{len(match_ids)} shop đã hoàn thành")

                    # Session refresh mỗi 100 shop để reset fingerprint
                    if shop_count % 100 == 0:
                        log(f"🔄 Refresh session sau {shop_count} shop để tránh detection...")
                        session.close()
                        session = requests.Session()

                        # Thêm delay lớn hơn sau khi refresh session
                        session_refresh_delay = random.uniform(8, 15)
                        log(f"⏳ Đợi {session_refresh_delay:.1f}s sau khi refresh session...")
                        time.sleep(session_refresh_delay)

                    # Delay giữa các shop để tránh quá aggressive
                    elif i < len(match_ids) - 1:  # Không delay ở shop cuối cùng
                        shop_delay = random.uniform(8, 15)  # 8-15 giây
                        log(f"⏳ Đợi {shop_delay:.1f}s trước khi xử lý shop tiếp theo...")
                        time.sleep(shop_delay)

                except Exception as e:
                    log(f"❌ Lỗi khi xử lý shop ID {match_id}: {str(e)}")

        # Hoàn tất và convert CSV sang Excel
        success = csv_excel_writer.finalize()
        if success:
            log(f"✅ Hoàn thành! Đã lưu {total_products} sản phẩm vào {output_filename}")
        return success

    except Exception as e:
        log(f"❌ Lỗi: {str(e)}")
        return False

def fetch_and_save_multiple_shops(username, password, match_ids, output_filename=None, headless=True, timeout=30, max_retries=3, log_callback=None, max_workers=8):
    """
    Lấy dữ liệu từ nhiều shop và lưu vào cùng một file Excel (sử dụng CSV then Excel để tối ưu)

    Args:
        username: Tên đăng nhập
        password: Mật khẩu
        match_ids: Danh sách ID shop
        output_filename: Tên file Excel đầu ra
        headless: Chạy ở chế độ headless
        timeout: Thời gian timeout
        max_retries: Số lần thử lại tối đa
        log_callback: Callback để ghi log
        max_workers: Số luồng tối đa chạy song song

    Returns:
        str: Tên file Excel đã lưu
    """
    def log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)

    if output_filename is None:
        # Tạo tên file với timestamp để tránh xung đột
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"autoshopee_products_{timestamp}.xlsx"

    # Sử dụng CSV then Excel để tối ưu tốc độ ghi
    success = fetch_all_products_from_multiple_shops_csv_then_excel(
        username,
        password,
        match_ids,
        output_filename,
        headless=headless,
        timeout=timeout,
        max_retries=max_retries,
        log_callback=log_callback,
        max_workers=max_workers
    )

    if success:
        return output_filename
    else:
        log("❌ Xử lý dữ liệu thất bại")
        return None
